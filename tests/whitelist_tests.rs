use macbook_cleaner::config::whitelist::Whitelist;
use std::path::{Path, PathBuf};

#[test]
fn test_whitelist_default() {
    let whitelist = Whitelist::default();
    
    // Check protected paths
    assert!(whitelist.protected_paths.contains(&"~/Documents".to_string()));
    assert!(whitelist.protected_paths.contains(&"~/Desktop".to_string()));
    assert!(whitelist.protected_paths.contains(&"/System".to_string()));
    
    // Check protected patterns
    assert!(whitelist.protected_patterns.contains(&"important".to_string()));
    assert!(whitelist.protected_patterns.contains(&"do-not-delete".to_string()));
    
    // Check protected extensions
    assert!(whitelist.protected_extensions.contains(&"key".to_string()));
    assert!(whitelist.protected_extensions.contains(&"pem".to_string()));
    
    // Check system directories
    assert!(whitelist.system_directories.contains(&"/System".to_string()));
    assert!(whitelist.system_directories.contains(&"/usr".to_string()));
    
    // Check user data directories
    assert!(whitelist.user_data_directories.contains(&"~/Documents".to_string()));
    assert!(whitelist.user_data_directories.contains(&"~/Desktop".to_string()));
}

#[test]
fn test_is_protected() -> anyhow::Result<()> {
    let whitelist = Whitelist::default();
    let home_dir = dirs::home_dir().unwrap();
    
    // Protected paths
    assert!(whitelist.is_protected(&home_dir.join("Documents"))?);
    assert!(whitelist.is_protected(&home_dir.join("Desktop"))?);
    assert!(whitelist.is_protected(Path::new("/System"))?);
    
    // Non-protected paths
    assert!(!whitelist.is_protected(&home_dir.join("Downloads/temp.txt"))?);
    assert!(!whitelist.is_protected(Path::new("/tmp/test.txt"))?);
    
    // Protected patterns
    assert!(whitelist.is_protected(Path::new("/tmp/important-file.txt"))?);
    assert!(whitelist.is_protected(Path::new("/tmp/do-not-delete.txt"))?);
    
    // Protected extensions
    assert!(whitelist.is_protected(Path::new("/tmp/certificate.key"))?);
    assert!(whitelist.is_protected(Path::new("/tmp/certificate.pem"))?);
    
    Ok(())
}

#[test]
fn test_is_system_directory() -> anyhow::Result<()> {
    let whitelist = Whitelist::default();
    
    // System directories
    assert!(whitelist.is_system_directory(Path::new("/System"))?);
    assert!(whitelist.is_system_directory(Path::new("/usr/bin"))?);
    assert!(whitelist.is_system_directory(Path::new("/bin"))?);
    
    // Non-system directories
    let home_dir = dirs::home_dir().unwrap();
    assert!(!whitelist.is_system_directory(&home_dir)?);
    assert!(!whitelist.is_system_directory(Path::new("/tmp"))?);
    
    Ok(())
}

#[test]
fn test_is_user_data_directory() -> anyhow::Result<()> {
    let whitelist = Whitelist::default();
    let home_dir = dirs::home_dir().unwrap();
    
    // User data directories
    assert!(whitelist.is_user_data_directory(&home_dir.join("Documents"))?);
    assert!(whitelist.is_user_data_directory(&home_dir.join("Desktop"))?);
    assert!(whitelist.is_user_data_directory(&home_dir.join("Pictures"))?);
    
    // Non-user data directories
    assert!(!whitelist.is_user_data_directory(&home_dir.join("Downloads"))?);
    assert!(!whitelist.is_user_data_directory(Path::new("/tmp"))?);
    
    Ok(())
}

#[test]
fn test_add_protected_path() {
    let mut whitelist = Whitelist::default();
    let test_path = "/test/path";
    
    // Path should not be in whitelist initially
    assert!(!whitelist.protected_paths.contains(&test_path.to_string()));
    
    // Add path
    whitelist.add_protected_path(test_path);
    
    // Path should now be in whitelist
    assert!(whitelist.protected_paths.contains(&test_path.to_string()));
    
    // Adding the same path again should not duplicate it
    whitelist.add_protected_path(test_path);
    assert_eq!(
        whitelist.protected_paths.iter().filter(|p| p == &&test_path.to_string()).count(),
        1
    );
}

#[test]
fn test_remove_protected_path() {
    let mut whitelist = Whitelist::default();
    let test_path = "~/Documents";
    
    // Path should be in whitelist initially
    assert!(whitelist.protected_paths.contains(&test_path.to_string()));
    
    // Remove path
    whitelist.remove_protected_path(test_path);
    
    // Path should no longer be in whitelist
    assert!(!whitelist.protected_paths.contains(&test_path.to_string()));
}
