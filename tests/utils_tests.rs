use macbook_cleaner::utils::FileUtils;
use std::path::Path;
use tempfile::tempdir;
use std::fs::{self, File};
use std::io::Write;

#[test]
fn test_format_size() {
    assert_eq!(FileUtils::format_size(0), "0 B");
    assert_eq!(FileUtils::format_size(1024), "1 KiB");
    assert_eq!(FileUtils::format_size(1024 * 1024), "1 MiB");
    assert_eq!(FileUtils::format_size(1024 * 1024 * 1024), "1 GiB");
}

#[test]
fn test_get_extension() {
    assert_eq!(FileUtils::get_extension(Path::new("test.txt")), Some("txt".to_string()));
    assert_eq!(FileUtils::get_extension(Path::new("test.TXT")), Some("txt".to_string()));
    assert_eq!(FileUtils::get_extension(Path::new("test")), None);
    assert_eq!(FileUtils::get_extension(Path::new(".hidden")), None);
    assert_eq!(FileUtils::get_extension(Path::new("test.tar.gz")), Some("gz".to_string()));
}

#[test]
fn test_is_hidden() {
    assert!(FileUtils::is_hidden(Path::new(".hidden")));
    assert!(!FileUtils::is_hidden(Path::new("visible")));
    assert!(!FileUtils::is_hidden(Path::new("visible.txt")));
    assert!(FileUtils::is_hidden(Path::new(".hidden.txt")));
}

#[test]
fn test_expand_tilde() {
    let home_dir = dirs::home_dir().unwrap();
    
    assert_eq!(FileUtils::expand_tilde("~/test"), home_dir.join("test"));
    assert_eq!(FileUtils::expand_tilde("/absolute/path"), Path::new("/absolute/path"));
    assert_eq!(FileUtils::expand_tilde("relative/path"), Path::new("relative/path"));
}

#[test]
fn test_ensure_dir_exists() -> anyhow::Result<()> {
    let temp_dir = tempdir()?;
    let test_dir = temp_dir.path().join("test_dir");
    
    // Directory doesn't exist yet
    assert!(!test_dir.exists());
    
    // Create directory
    FileUtils::ensure_dir_exists(&test_dir)?;
    
    // Directory should now exist
    assert!(test_dir.exists());
    assert!(test_dir.is_dir());
    
    // Calling again on existing directory should be fine
    FileUtils::ensure_dir_exists(&test_dir)?;
    
    Ok(())
}

#[test]
fn test_is_empty_dir() -> anyhow::Result<()> {
    let temp_dir = tempdir()?;
    
    // Empty directory
    assert!(FileUtils::is_empty_dir(temp_dir.path())?);
    
    // Create a file in the directory
    let test_file = temp_dir.path().join("test.txt");
    let mut file = File::create(&test_file)?;
    writeln!(file, "test content")?;
    
    // Directory is no longer empty
    assert!(!FileUtils::is_empty_dir(temp_dir.path())?);
    
    // Non-directory should return false
    assert!(!FileUtils::is_empty_dir(&test_file)?);
    
    Ok(())
}

#[test]
fn test_get_mime_type() {
    assert_eq!(FileUtils::get_mime_type(Path::new("test.txt")), "text/plain");
    assert_eq!(FileUtils::get_mime_type(Path::new("test.html")), "text/html");
    assert_eq!(FileUtils::get_mime_type(Path::new("test.jpg")), "image/jpeg");
    assert_eq!(FileUtils::get_mime_type(Path::new("test.png")), "image/png");
    assert_eq!(FileUtils::get_mime_type(Path::new("test.pdf")), "application/pdf");
    assert_eq!(FileUtils::get_mime_type(Path::new("test.unknown")), "application/octet-stream");
}
