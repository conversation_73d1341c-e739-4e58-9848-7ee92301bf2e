use macbook_cleaner::config::Config;
use std::path::Path;

#[test]
fn test_default_config_values() {
    let config = Config::default();

    assert!(config.general.backup_before_delete);
    assert_eq!(config.general.backup_retention_days, 7);
    assert!(config.general.confirm_deletions);
    assert_eq!(config.general.max_file_age_days, 30);

    assert!(config.categories.cache.enabled);
    assert!(config.categories.cache.auto_clean);
    assert!(config.categories.logs.enabled);
    assert!(config.categories.logs.auto_clean);
    assert_eq!(config.categories.logs.keep_days, 7);
    assert!(config.categories.temp.enabled);
    assert!(config.categories.temp.auto_clean);
    assert!(!config.categories.downloads.enabled);
    assert!(!config.categories.downloads.auto_clean);
    assert_eq!(config.categories.downloads.age_threshold, 90);

    assert_eq!(config.safety.protected_paths.len(), 9);
    assert!(config.safety.protected_paths.contains(&"~/Documents".to_string()));
    assert!(config.safety.protected_paths.contains(&"/Applications".to_string()));

    assert_eq!(config.thresholds.large_file_size_gb, 1.0);
    assert_eq!(config.thresholds.duplicate_min_size_mb, 10.0);
}

#[test]
fn test_config_load_and_save() {
    let temp_dir = tempfile::tempdir().unwrap();
    let config_path = temp_dir.path().join("test_config.toml");

    let mut config = Config::default();
    config.general.backup_retention_days = 14;
    config.save_to_file(&config_path).unwrap();

    let loaded_config = Config::from_file(&config_path).unwrap();
    assert_eq!(loaded_config.general.backup_retention_days, 14);
}

#[test]
fn test_config_load_or_create_default() {
    let temp_dir = tempfile::tempdir().unwrap();
    let config_dir = temp_dir.path().join(".macbook-cleaner");
    let config_path = config_dir.join("config.toml");

    // Ensure the directory exists for the test
    std::fs::create_dir_all(&config_dir).unwrap();

    // Should create default config if not exists
    std::env::set_var("MACBOOK_CLEANER_CONFIG_DIR", &config_dir);

    // Should create default config if not exists
    let config = Config::load_or_create_default().unwrap();
    assert!(config_path.exists());
    assert_eq!(config.general.backup_retention_days, 7);

    // Should load existing config
    let mut modified_config = Config::default();
    modified_config.general.backup_retention_days = 21;
    modified_config.save_to_file(&config_path).unwrap();

    let loaded_config = Config::load_or_create_default().unwrap();
    assert_eq!(loaded_config.general.backup_retention_days, 21);

    std::env::remove_var("MACBOOK_CLEANER_CONFIG_DIR");
}