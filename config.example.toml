# MacBook Cleaner Configuration File
# Copy this file to ~/.macbook-cleaner/config.toml and customize as needed

[general]
# Create backup before deleting files
backup_before_delete = true

# Number of days to keep backups (set to 0 to disable automatic cleanup)
backup_retention_days = 0

# Ask for confirmation before deleting files
confirm_deletions = true

# Maximum age in days for files to be considered for deletion
max_file_age_days = 30

[categories]
# Cache files configuration
[categories.cache]
enabled = true
auto_clean = true

# Log files configuration
[categories.logs]
enabled = true
auto_clean = true
# Keep log files newer than this many days
keep_days = 7

# Temporary files configuration
[categories.temp]
enabled = true
auto_clean = true

# Downloads folder configuration
[categories.downloads]
# Disabled by default for safety
enabled = false
auto_clean = false
# Only consider files older than this many days
age_threshold = 90

[safety]
# Protected paths that should never be deleted
protected_paths = [
    "~/Documents",
    "~/Desktop", 
    "~/Pictures",
    "~/Music",
    "~/Movies",
    "/System/Library/CoreServices",
    "/Applications",
    "/usr/bin",
    "/usr/sbin",
    "/bin",
    "/sbin"
]

[thresholds]
# Files larger than this size (in GB) will be flagged as large files
large_file_size_gb = 1.0

# Only consider files larger than this size (in MB) when finding duplicates
duplicate_min_size_mb = 10.0

# Example of additional protected paths you might want to add:
# protected_paths = [
#     "~/Documents",
#     "~/Desktop",
#     "~/Pictures", 
#     "~/Music",
#     "~/Movies",
#     "~/Development",
#     "~/Work",
#     "/System/Library/CoreServices",
#     "/Applications",
#     "/usr/bin",
#     "/usr/sbin",
#     "/bin",
#     "/sbin"
# ]

# Example of customizing category settings:
# [categories.cache]
# enabled = true
# auto_clean = false  # Require manual confirmation

# [categories.logs] 
# enabled = true
# auto_clean = true
# keep_days = 14  # Keep logs for 2 weeks instead of 1

# [categories.downloads]
# enabled = true
# auto_clean = false
# age_threshold = 30  # Consider files older than 30 days instead of 90

# Example of adjusting thresholds:
# [thresholds]
# large_file_size_gb = 0.5  # Flag files larger than 500MB
# duplicate_min_size_mb = 5.0  # Find duplicates of files larger than 5MB
