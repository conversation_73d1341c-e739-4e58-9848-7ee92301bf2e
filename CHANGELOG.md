# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Initial project structure and core modules
- CLI interface with clap for command parsing
- Configuration system with TOML support
- File scanning capabilities for various categories:
  - System cache and temporary files
  - Application-specific cleanup (Xcode, Node.js, Python, Docker, Homebrew)
  - User data cleanup (Downloads, Trash, Browser caches)
  - Development environment cleanup
  - System maintenance (language files, duplicates, large files)
- Safety mechanisms:
  - Whitelist system for protected files and directories
  - Dry run mode for previewing changes
  - Backup creation before deletion
  - User confirmation prompts
- File analysis capabilities:
  - Disk usage analysis
  - Large file detection
  - Duplicate file detection using SHA256 hashing
  - File type categorization
- Progress reporting with indicatif progress bars
- Comprehensive unit tests
- Example configuration file
- Documentation and README

### Security
- Protected paths system to prevent deletion of critical files
- File-in-use detection to avoid deleting active files
- Critical system file detection
- Permission checks before deletion

## [0.1.0] - 2024-07-15

### Added
- Initial release with core functionality
- Basic CLI commands: scan, clean, analyze, config, restore
- Configuration management
- Safety features and backup system
- File scanning and analysis
- Progress reporting
- Unit tests and documentation
