# MacBook Storage Cleaner CLI Tool - Project Plan

## Project Overview

A comprehensive command-line tool for macOS that safely identifies, analyzes, and removes unnecessary files to free up disk space without compromising system functionality or user data.

## Core Objectives

- **Primary Goal**: Reclaim maximum disk space safely
- **Safety First**: Never delete critical system files or user data
- **Transparency**: Show exactly what will be deleted before action
- **Performance**: Fast scanning and cleaning operations
- **User Control**: Granular control over what gets cleaned

## Target File Categories

### 1. System Cache & Temporary Files
- `~/Library/Caches/*` - Application caches
- `/System/Library/Caches/*` - System caches
- `/private/var/folders/*` - Temporary system files
- `/tmp/*` - Temporary files
- `~/Library/Logs/*` - Application logs
- `/var/log/*` - System logs (older than 30 days)

### 2. Application-Specific Cleanup
- **Xcode**: Derived data, archives, device support files
  - `~/Library/Developer/Xcode/DerivedData/*`
  - `~/Library/Developer/Xcode/Archives/*`
  - `~/Library/Developer/Xcode/iOS DeviceSupport/*`
- **Node.js**: node_modules in inactive projects
- **Python**: __pycache__, .pyc files
- **Docker**: Unused images, containers, volumes
- **Homebrew**: Old formula versions, cache

### 3. User Data Cleanup
- **Downloads**: Files older than specified days
- **Trash**: Empty all trash cans
- **Mail**: Downloaded attachments, mail cache
- **Photos**: Recently deleted album
- **Safari**: Cache, history, downloads
- **Chrome/Firefox**: Cache, history, downloads

### 4. Development Environment
- **Git repositories**: .git/objects optimization
- **IDE caches**: VSCode, IntelliJ, etc.
- **Build artifacts**: target/, build/, dist/ folders
- **Package managers**: npm cache, pip cache, gem cache

### 5. System Maintenance
- **Language files**: Unused localizations
- **Universal binaries**: Remove unused architectures
- **Duplicate files**: Identify and remove duplicates
- **Large files**: Identify files >1GB for review

## Technical Architecture

### Core Components

```
src/
├── main.rs                 # CLI entry point
├── scanner/
│   ├── mod.rs             # Scanner module
│   ├── cache_scanner.rs   # Cache file detection
│   ├── app_scanner.rs     # App-specific cleanup
│   ├── user_scanner.rs    # User data analysis
│   └── system_scanner.rs  # System file analysis
├── cleaner/
│   ├── mod.rs             # Cleaner module
│   ├── safe_delete.rs     # Safe deletion logic
│   └── backup.rs          # Backup before delete
├── analyzer/
│   ├── mod.rs             # Analysis module
│   ├── size_analyzer.rs   # Disk usage analysis
│   └── duplicate_finder.rs # Duplicate detection
├── config/
│   ├── mod.rs             # Configuration
│   ├── rules.rs           # Cleanup rules
│   └── whitelist.rs       # Protected files
└── utils/
    ├── mod.rs             # Utilities
    ├── file_utils.rs      # File operations
    └── progress.rs        # Progress reporting
```

### Safety Mechanisms

1. **Whitelist System**: Protected files/directories
2. **Dry Run Mode**: Preview before actual deletion
3. **Backup Creation**: Optional backup of deleted files
4. **Confirmation Prompts**: User confirmation for risky operations
5. **Rollback Capability**: Restore accidentally deleted files

## CLI Interface Design

### Main Commands

```bash
# Scan and analyze disk usage
macbook-cleaner scan [--deep] [--category <category>]

# Clean with interactive mode
macbook-cleaner clean [--dry-run] [--auto] [--category <category>]

# Analyze specific directories
macbook-cleaner analyze <path> [--duplicates] [--large-files]

# Configuration management
macbook-cleaner config [--set <key=value>] [--show]

# Restore from backup
macbook-cleaner restore [--list] [--file <backup-id>]
```

### Command Examples

```bash
# Quick scan of all categories
macbook-cleaner scan

# Deep scan with detailed analysis
macbook-cleaner scan --deep

# Clean only cache files (dry run)
macbook-cleaner clean --category cache --dry-run

# Auto-clean safe categories
macbook-cleaner clean --auto --category "cache,logs,temp"

# Find duplicates in Downloads
macbook-cleaner analyze ~/Downloads --duplicates

# Interactive cleaning session
macbook-cleaner clean
```

## Configuration System

### Default Configuration (`~/.macbook-cleaner/config.toml`)

```toml
[general]
backup_before_delete = true
backup_retention_days = 30
confirm_deletions = true
max_file_age_days = 30

[categories]
cache = { enabled = true, auto_clean = true }
logs = { enabled = true, auto_clean = true, keep_days = 7 }
temp = { enabled = true, auto_clean = true }
downloads = { enabled = false, auto_clean = false, age_threshold = 90 }

[safety]
protected_paths = [
    "~/Documents",
    "~/Desktop",
    "~/Pictures",
    "/System/Library/CoreServices"
]

[thresholds]
large_file_size_gb = 1.0
duplicate_min_size_mb = 10.0
```

## Safety Features

### 1. Protected Paths
- System critical directories
- User data directories
- Application bundles
- Currently running applications

### 2. File Analysis
- Check if file is in use
- Verify file permissions
- Detect system dependencies
- Age-based filtering

### 3. Backup System
- Create timestamped backups
- Compressed storage
- Automatic cleanup of old backups
- Quick restore functionality

## Implementation Phases

### Phase 1: Core Infrastructure (Week 1-2)
- [ ] Project setup and CLI framework
- [ ] Basic file scanning capabilities
- [ ] Configuration system
- [ ] Safety mechanisms and whitelists

### Phase 2: Basic Cleaning (Week 3-4)
- [ ] Cache file detection and removal
- [ ] Temporary file cleanup
- [ ] Log file management
- [ ] Trash emptying

### Phase 3: Advanced Features (Week 5-6)
- [ ] Application-specific cleaners
- [ ] Duplicate file detection
- [ ] Large file identification
- [ ] Development environment cleanup

### Phase 4: User Experience (Week 7-8)
- [ ] Interactive mode with progress bars
- [ ] Detailed reporting and statistics
- [ ] Backup and restore functionality
- [ ] Performance optimizations

### Phase 5: Testing & Polish (Week 9-10)
- [ ] Comprehensive testing on various macOS versions
- [ ] Error handling and edge cases
- [ ] Documentation and help system
- [ ] Performance benchmarking

## Risk Mitigation

### High-Risk Operations
1. **System file deletion**: Strict whitelist enforcement
2. **Application data**: User confirmation required
3. **Large deletions**: Progress tracking and cancellation
4. **Permission issues**: Graceful error handling

### Testing Strategy
- Unit tests for all core functions
- Integration tests with mock file systems
- Manual testing on clean macOS installations
- Beta testing with power users

## Success Metrics

### Primary Metrics
- **Storage Reclaimed**: Average GB freed per run
- **Safety Score**: Zero critical file deletions
- **Performance**: Scan time < 30 seconds for typical system
- **User Satisfaction**: Positive feedback on ease of use

### Secondary Metrics
- **Adoption Rate**: Downloads and active users
- **Error Rate**: < 1% of operations result in errors
- **Recovery Success**: 100% successful backup restores

## Future Enhancements

### Version 2.0 Features
- GUI application with same engine
- Scheduled automatic cleaning
- Cloud storage integration
- Network drive cleanup
- Advanced duplicate detection algorithms

### Integration Possibilities
- macOS System Preferences integration
- Homebrew formula distribution
- CI/CD pipeline integration for developers
- Integration with backup solutions

## Technical Requirements

### Dependencies
- Rust 1.70+ for performance and safety
- `clap` for CLI argument parsing
- `tokio` for async operations
- `serde` for configuration management
- `indicatif` for progress bars

### System Requirements
- macOS 10.15 (Catalina) or later
- Administrator privileges for system-wide cleaning
- Minimum 100MB free space for operation

### Performance Targets
- Scan 1TB drive in < 60 seconds
- Memory usage < 100MB during operation
- CPU usage < 50% during intensive operations

## Deployment Strategy

### Distribution Methods
1. **Homebrew**: Primary distribution channel
2. **GitHub Releases**: Direct binary downloads
3. **Cargo**: For Rust developers
4. **Mac App Store**: Future GUI version

### Installation Process
```bash
# Via Homebrew (recommended)
brew install macbook-cleaner

# Via Cargo
cargo install macbook-cleaner

# Direct download
curl -L https://github.com/user/macbook-cleaner/releases/latest/download/macbook-cleaner-macos.tar.gz | tar xz
```

This project plan provides a comprehensive roadmap for building a safe, effective, and user-friendly macOS storage cleaner that will help users reclaim significant disk space while maintaining system integrity.