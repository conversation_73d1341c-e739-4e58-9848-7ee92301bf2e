# MacBook Cleaner

A comprehensive command-line tool for macOS that safely identifies, analyzes, and removes unnecessary files to free up disk space without compromising system functionality or user data.

## Features

- **Safe Cleaning**: Never deletes critical system files or user data
- **Comprehensive Scanning**: Identifies various types of unnecessary files
- **Detailed Analysis**: Shows exactly what will be deleted before action
- **Fast Performance**: Optimized for speed with minimal system impact
- **User Control**: Granular control over what gets cleaned

## File Categories

The tool can clean the following types of files:

1. **System Cache & Temporary Files**
   - Application caches
   - System caches
   - Temporary system files
   - Log files

2. **Application-Specific Cleanup**
   - Xcode: Derived data, archives, device support files
   - Node.js: node_modules in inactive projects
   - Python: __pycache__, .pyc files
   - Docker: Unused images, containers, volumes
   - Homebrew: Old formula versions, cache

3. **User Data Cleanup**
   - Downloads: Files older than specified days
   - Trash: Empty all trash cans
   - Mail: Downloaded attachments, mail cache
   - Browser caches and history

4. **Development Environment**
   - Git repositories optimization
   - IDE caches
   - Build artifacts
   - Package manager caches

5. **System Maintenance**
   - Language files (unused localizations)
   - Duplicate files identification
   - Large files identification

## Usage

Since you're running this locally, use `./target/release/macbook-cleaner` instead of `macbook-cleaner`:

```bash
# Scan and analyze disk usage
./target/release/macbook-cleaner scan [--deep] [--category <category>]

# Clean with interactive mode
./target/release/macbook-cleaner clean [--dry-run] [--auto] [--category <category>]

# Analyze specific directories
./target/release/macbook-cleaner analyze <path> [--duplicates] [--large-files]

# Configuration management
./target/release/macbook-cleaner config [--set <key=value>] [--show]

# Restore from backup
./target/release/macbook-cleaner restore [--list] [--file <backup-id>]
```

### Examples

```bash
# Quick scan of all categories
./target/release/macbook-cleaner scan

# Deep scan with detailed analysis
./target/release/macbook-cleaner scan --deep

# Clean only cache files (dry run)
./target/release/macbook-cleaner clean --category cache --dry-run

# Auto-clean safe categories
./target/release/macbook-cleaner clean --auto

# Find duplicates in Downloads
./target/release/macbook-cleaner analyze ~/Downloads --duplicates

# Find large files in your home directory
./target/release/macbook-cleaner analyze ~ --large-files

# Interactive cleaning session
./target/release/macbook-cleaner clean

# Clean specific category (cache, apps, system, user)
./target/release/macbook-cleaner clean --category cache --auto
```

## Installation

### Prerequisites

- macOS 10.15 (Catalina) or later
- Rust 1.70+ (if building from source)

### Option 1: Build from Source (Recommended for now)

```bash
# Clone the repository
git clone https://github.com/user/macbook-cleaner.git
cd macbook-cleaner

# Build the project
cargo build --release

# Install to your PATH
cargo install --path .
```

### Option 2: Via Cargo (Future)

```bash
cargo install macbook-cleaner
```

### Option 3: Via Homebrew (Future)

```bash
brew install macbook-cleaner
```

### Option 4: Direct Download (Future)

```bash
curl -L https://github.com/user/macbook-cleaner/releases/latest/download/macbook-cleaner-macos.tar.gz | tar xz
```

## Safety Features

- **Whitelist System**: Protected files/directories
- **Dry Run Mode**: Preview before actual deletion
- **Backup Creation**: Optional backup of deleted files
- **Confirmation Prompts**: User confirmation for risky operations
- **Rollback Capability**: Restore accidentally deleted files

## Configuration

The default configuration file is located at `~/.macbook-cleaner/config.toml`. You can customize settings like:

- Backup retention period
- File age thresholds
- Protected paths
- Category-specific settings

### Creating Configuration

Copy the example configuration file:

```bash
cp config.example.toml ~/.macbook-cleaner/config.toml
```

Then edit the file to customize settings:

```bash
nano ~/.macbook-cleaner/config.toml
```

### Key Configuration Options

- `backup_before_delete`: Create backups before deleting files
- `backup_retention_days`: How long to keep backups
- `confirm_deletions`: Ask for confirmation before deleting
- `protected_paths`: Directories that should never be cleaned
- `large_file_size_gb`: Threshold for flagging large files
- `duplicate_min_size_mb`: Minimum size for duplicate detection

## Development

### Running Tests

```bash
# Run all tests
cargo test

# Run specific test
cargo test config_tests
```

### Building Documentation

```bash
# Generate documentation
cargo doc --no-deps --open
```

### Project Structure

```
src/
├── main.rs                 # CLI entry point
├── lib.rs                  # Library exports
├── scanner/                # File scanning modules
├── cleaner/                # File cleaning modules
├── analyzer/               # Analysis modules
├── config/                 # Configuration handling
└── utils/                  # Utility functions
```

## Requirements

- macOS 10.15 (Catalina) or later
- Administrator privileges for system-wide cleaning
- Minimum 100MB free space for operation

## License

MIT License
