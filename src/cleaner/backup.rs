use anyhow::Result;
use std::path::{Path, PathBuf};
use std::fs::File;
use std::io::{Write, Read};
use chrono::{DateTime, Utc};
use crate::config::Config;
use crate::scanner::FileInfo;

pub struct BackupManager {
    config: Config,
    backup_dir: PathBuf,
}

impl BackupManager {
    pub fn new(config: &Config) -> Self {
        let home_dir = dirs::home_dir().unwrap_or_else(|| PathBuf::from("/tmp"));
        let backup_dir = home_dir.join(".macbook-cleaner/backups");
        
        Self {
            config: config.clone(),
            backup_dir,
        }
    }
    
    pub async fn create_backup(&self, files: &[FileInfo]) -> Result<String> {
        // Create backup directory if it doesn't exist
        std::fs::create_dir_all(&self.backup_dir)?;
        
        // Generate backup ID with timestamp
        let timestamp = Utc::now().format("%Y%m%d_%H%M%S");
        let backup_id = format!("backup_{}", timestamp);
        let backup_path = self.backup_dir.join(&backup_id);
        
        // Create backup archive
        self.create_backup_archive(&backup_path, files).await?;
        
        // Create metadata file
        self.create_backup_metadata(&backup_path, files).await?;
        
        log::info!("Created backup: {}", backup_id);
        Ok(backup_id)
    }
    
    async fn create_backup_archive(&self, backup_path: &Path, files: &[FileInfo]) -> Result<()> {
        let archive_path = backup_path.with_extension("tar.gz");
        let tar_gz = File::create(&archive_path)?;
        let enc = flate2::write::GzEncoder::new(tar_gz, flate2::Compression::default());
        let mut tar = tar::Builder::new(enc);
        
        for file_info in files {
            if file_info.path.exists() {
                if file_info.path.is_file() {
                    // Add file to archive
                    let mut file = File::open(&file_info.path)?;
                    let relative_path = self.make_relative_path(&file_info.path)?;
                    tar.append_file(&relative_path, &mut file)?;
                } else if file_info.path.is_dir() {
                    // Add directory recursively
                    let relative_path = self.make_relative_path(&file_info.path)?;
                    tar.append_dir_all(&relative_path, &file_info.path)?;
                }
            }
        }
        
        tar.finish()?;
        Ok(())
    }
    
    async fn create_backup_metadata(&self, backup_path: &Path, files: &[FileInfo]) -> Result<()> {
        let metadata_path = backup_path.with_extension("json");
        
        let metadata = BackupMetadata {
            created_at: Utc::now(),
            file_count: files.len(),
            total_size: files.iter().map(|f| f.size).sum(),
            files: files.iter().map(|f| BackupFileInfo {
                original_path: f.path.clone(),
                size: f.size,
                category: f.category.clone(),
                description: f.description.clone(),
            }).collect(),
        };
        
        let json = serde_json::to_string_pretty(&metadata)?;
        std::fs::write(metadata_path, json)?;
        
        Ok(())
    }
    
    fn make_relative_path(&self, path: &Path) -> Result<PathBuf> {
        // Convert absolute path to relative path for archive
        if path.is_absolute() {
            if let Ok(relative) = path.strip_prefix("/") {
                Ok(relative.to_path_buf())
            } else {
                Ok(path.to_path_buf())
            }
        } else {
            Ok(path.to_path_buf())
        }
    }
    
    pub fn list_backups(&self) -> Result<()> {
        if !self.backup_dir.exists() {
            println!("No backups found.");
            return Ok(());
        }
        
        let mut backups = Vec::new();
        
        for entry in std::fs::read_dir(&self.backup_dir)? {
            let entry = entry?;
            let path = entry.path();
            
            if path.extension().and_then(|s| s.to_str()) == Some("json") {
                if let Ok(metadata) = self.load_backup_metadata(&path) {
                    let backup_id = path.file_stem()
                        .and_then(|s| s.to_str())
                        .unwrap_or("unknown");
                    
                    backups.push((backup_id.to_string(), metadata));
                }
            }
        }
        
        if backups.is_empty() {
            println!("No backups found.");
            return Ok(());
        }
        
        // Sort by creation time (newest first)
        backups.sort_by(|a, b| b.1.created_at.cmp(&a.1.created_at));
        
        println!("\n📦 Available Backups");
        println!("===================");
        
        for (backup_id, metadata) in backups {
            println!("ID: {}", backup_id);
            println!("  Created: {}", metadata.created_at.format("%Y-%m-%d %H:%M:%S UTC"));
            println!("  Files: {}", metadata.file_count);
            println!("  Size: {}", bytesize::ByteSize(metadata.total_size));
            println!();
        }
        
        Ok(())
    }
    
    pub fn restore_backup(&self, backup_id: &str) -> Result<()> {
        let archive_path = self.backup_dir.join(backup_id).with_extension("tar.gz");
        let metadata_path = self.backup_dir.join(backup_id).with_extension("json");
        
        if !archive_path.exists() || !metadata_path.exists() {
            return Err(anyhow::anyhow!("Backup not found: {}", backup_id));
        }
        
        // Load metadata
        let metadata = self.load_backup_metadata(&metadata_path)?;
        
        // Show restore preview
        println!("\n🔄 Restore Preview");
        println!("==================");
        println!("Backup ID: {}", backup_id);
        println!("Created: {}", metadata.created_at.format("%Y-%m-%d %H:%M:%S UTC"));
        println!("Files to restore: {}", metadata.file_count);
        println!("Total size: {}", bytesize::ByteSize(metadata.total_size));
        
        // Get user confirmation
        if !self.get_restore_confirmation()? {
            println!("Restore cancelled by user.");
            return Ok(());
        }
        
        // Perform restore
        self.extract_backup_archive(&archive_path)?;
        
        println!("✅ Backup restored successfully!");
        println!("Restored {} files", metadata.file_count);
        
        Ok(())
    }
    
    fn load_backup_metadata(&self, metadata_path: &Path) -> Result<BackupMetadata> {
        let json = std::fs::read_to_string(metadata_path)?;
        let metadata: BackupMetadata = serde_json::from_str(&json)?;
        Ok(metadata)
    }
    
    fn extract_backup_archive(&self, archive_path: &Path) -> Result<()> {
        let tar_gz = File::open(archive_path)?;
        let tar = flate2::read::GzDecoder::new(tar_gz);
        let mut archive = tar::Archive::new(tar);
        
        // Extract to root directory
        archive.unpack("/")?;
        
        Ok(())
    }
    
    fn get_restore_confirmation(&self) -> Result<bool> {
        use std::io::{self, Write};
        
        print!("This will restore files to their original locations. Continue? [y/N]: ");
        io::stdout().flush()?;
        
        let mut input = String::new();
        io::stdin().read_line(&mut input)?;
        
        Ok(input.trim().to_lowercase() == "y" || input.trim().to_lowercase() == "yes")
    }
    
    /// Clean up old backups based on retention policy
    pub fn cleanup_old_backups(&self) -> Result<()> {
        if !self.backup_dir.exists() {
            return Ok(());
        }

        let retention_days = self.config.general.backup_retention_days;

        // If retention_days is 0, never auto-delete backups
        if retention_days == 0 {
            println!("Backup retention is disabled (set to 0 days). Backups will not be automatically deleted.");
            return Ok(());
        }

        let cutoff_time = Utc::now() - chrono::Duration::days(retention_days as i64);
        
        let mut removed_count = 0;
        
        for entry in std::fs::read_dir(&self.backup_dir)? {
            let entry = entry?;
            let path = entry.path();
            
            if path.extension().and_then(|s| s.to_str()) == Some("json") {
                if let Ok(metadata) = self.load_backup_metadata(&path) {
                    if metadata.created_at < cutoff_time {
                        let backup_id = path.file_stem()
                            .and_then(|s| s.to_str())
                            .unwrap_or("unknown");
                        
                        // Remove both metadata and archive files
                        let archive_path = self.backup_dir.join(backup_id).with_extension("tar.gz");
                        
                        if path.exists() {
                            std::fs::remove_file(&path)?;
                        }
                        if archive_path.exists() {
                            std::fs::remove_file(&archive_path)?;
                        }
                        
                        removed_count += 1;
                        log::info!("Removed old backup: {}", backup_id);
                    }
                }
            }
        }
        
        if removed_count > 0 {
            println!("Cleaned up {} old backup(s)", removed_count);
        }
        
        Ok(())
    }
}

#[derive(serde::Serialize, serde::Deserialize)]
struct BackupMetadata {
    created_at: DateTime<Utc>,
    file_count: usize,
    total_size: u64,
    files: Vec<BackupFileInfo>,
}

#[derive(serde::Serialize, serde::Deserialize)]
struct BackupFileInfo {
    original_path: PathBuf,
    size: u64,
    category: String,
    description: String,
}
