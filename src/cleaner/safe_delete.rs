use anyhow::Result;
use std::path::Path;
use crate::config::Config;
use crate::scanner::FileInfo;

pub struct SafeDeleter {
    config: Config,
}

impl SafeDeleter {
    pub fn new(config: &Config) -> Self {
        Self {
            config: config.clone(),
        }
    }

    pub async fn delete_file(&self, file_info: &FileInfo) -> Result<u64> {
        let path = &file_info.path;

        // Final safety checks before deletion
        self.perform_safety_checks(path)?;

        // Get file size before deletion
        let file_size = std::fs::metadata(path)?.len();

        // Perform the actual deletion
        self.delete_file_or_directory(path)?;

        log::info!("Successfully deleted: {}", path.display());
        Ok(file_size)
    }

    fn perform_safety_checks(&self, path: &Path) -> Result<()> {
        // Check if file exists
        if !path.exists() {
            return Err(anyhow::anyhow!("File does not exist: {}", path.display()));
        }

        // Check if file is in protected paths
        for protected_path in &self.config.safety.protected_paths {
            let protected = Path::new(protected_path);
            if path.starts_with(protected) {
                return Err(anyhow::anyhow!("File is in protected path: {}", path.display()));
            }
        }

        // Check if file is currently in use
        if self.is_file_in_use(path)? {
            return Err(anyhow::anyhow!("File is currently in use: {}", path.display()));
        }

        // Check if file is a critical system file
        if self.is_critical_system_file(path)? {
            return Err(anyhow::anyhow!("File is a critical system file: {}", path.display()));
        }

        // Check permissions
        if !self.has_delete_permission(path)? {
            return Err(anyhow::anyhow!("Insufficient permissions to delete: {}", path.display()));
        }

        Ok(())
    }

    fn delete_file_or_directory(&self, path: &Path) -> Result<()> {
        if path.is_dir() {
            std::fs::remove_dir_all(path)?;
        } else {
            std::fs::remove_file(path)?;
        }
        Ok(())
    }

    fn is_file_in_use(&self, path: &Path) -> Result<bool> {
        // Simplified check - in production, you'd want more sophisticated detection
        if path.is_file() {
            match std::fs::OpenOptions::new()
                .write(true)
                .truncate(false)
                .open(path)
            {
                Ok(_) => Ok(false), // File is not in use
                Err(e) => {
                    match e.kind() {
                        std::io::ErrorKind::PermissionDenied => Ok(true),
                        _ => Ok(false),
                    }
                }
            }
        } else {
            Ok(false)
        }
    }

    fn is_critical_system_file(&self, path: &Path) -> Result<bool> {
        let path_str = path.to_string_lossy().to_lowercase();

        let critical_patterns = vec![
            "/system/library/coreservices",
            "/system/library/frameworks",
            "/usr/bin",
            "/usr/sbin",
            "/bin",
            "/sbin",
            "kernel",
            "boot.efi",
        ];

        for pattern in critical_patterns {
            if path_str.contains(pattern) {
                return Ok(true);
            }
        }

        Ok(false)
    }

    fn has_delete_permission(&self, path: &Path) -> Result<bool> {
        if let Some(parent) = path.parent() {
            match std::fs::metadata(parent) {
                Ok(metadata) => {
                    let permissions = metadata.permissions();
                    use std::os::unix::fs::PermissionsExt;
                    let mode = permissions.mode();
                    Ok((mode & 0o200) != 0)
                },
                Err(_) => Ok(false),
            }
        } else {
            Ok(false)
        }
    }

    /// Move file to trash instead of permanent deletion
    pub async fn move_to_trash(&self, file_info: &FileInfo) -> Result<u64> {
        let path = &file_info.path;
        self.perform_safety_checks(path)?;

        let file_size = std::fs::metadata(path)?.len();
        self.move_file_to_trash(path)?;

        log::info!("Successfully moved to trash: {}", path.display());
        Ok(file_size)
    }

    fn move_file_to_trash(&self, path: &Path) -> Result<()> {
        let home_dir = dirs::home_dir().ok_or_else(|| anyhow::anyhow!("Could not find home directory"))?;
        let trash_dir = home_dir.join(".Trash");

        std::fs::create_dir_all(&trash_dir)?;

        let file_name = path.file_name()
            .ok_or_else(|| anyhow::anyhow!("Could not get file name"))?;

        let mut trash_path = trash_dir.join(file_name);
        let mut counter = 1;

        while trash_path.exists() {
            let stem = path.file_stem()
                .and_then(|s| s.to_str())
                .unwrap_or("file");
            let extension = path.extension()
                .and_then(|s| s.to_str())
                .unwrap_or("");

            let new_name = if extension.is_empty() {
                format!("{} {}", stem, counter)
            } else {
                format!("{} {}.{}", stem, counter, extension)
            };

            trash_path = trash_dir.join(new_name);
            counter += 1;
        }

        std::fs::rename(path, trash_path)?;
        Ok(())
    }
}
