use anyhow::Result;
use crate::config::Config;
use crate::scanner::{ScanResults, FileInfo};

pub mod safe_delete;
pub mod backup;

use safe_delete::SafeDeleter;
use crate::cleaner::backup::BackupManager;

/// Main cleaner orchestrator
pub struct Cleaner {
    safe_deleter: SafeDeleter,
    backup_manager: BackupManager,
    config: Config,
}

impl Cleaner {
    pub fn new(config: &Config) -> Self {
        Self {
            safe_deleter: SafeDeleter::new(config),
            backup_manager: BackupManager::new(config),
            config: config.clone(),
        }
    }
    
    pub async fn clean_files(&self, files: Vec<FileInfo>, dry_run: bool) -> Result<CleanResults> {
        let mut results = CleanResults::new();
        
        if dry_run {
            return self.dry_run_clean(files).await;
        }
        
        // Create backup if enabled
        if self.config.general.backup_before_delete {
            let backup_id = self.backup_manager.create_backup(&files).await?;
            results.backup_id = Some(backup_id);
        }
        
        // Delete files safely
        for file in files {
            match self.safe_deleter.delete_file(&file).await {
                Ok(deleted_size) => {
                    results.deleted_files += 1;
                    results.freed_space += deleted_size;
                    results.successful_deletions.push(file.path.clone());
                },
                Err(e) => {
                    results.failed_deletions.push((file.path.clone(), e.to_string()));
                }
            }
        }
        
        Ok(results)
    }
    
    async fn dry_run_clean(&self, files: Vec<FileInfo>) -> Result<CleanResults> {
        let mut results = CleanResults::new();
        
        for file in files {
            if file.safe_to_delete {
                results.deleted_files += 1;
                results.freed_space += file.size;
                results.successful_deletions.push(file.path.clone());
            } else {
                results.skipped_files.push((file.path.clone(), "Not safe to delete".to_string()));
            }
        }
        
        results.is_dry_run = true;
        Ok(results)
    }
}

#[derive(Debug, Default)]
pub struct CleanResults {
    pub deleted_files: usize,
    pub freed_space: u64,
    pub successful_deletions: Vec<std::path::PathBuf>,
    pub failed_deletions: Vec<(std::path::PathBuf, String)>,
    pub skipped_files: Vec<(std::path::PathBuf, String)>,
    pub backup_id: Option<String>,
    pub is_dry_run: bool,
}

impl CleanResults {
    pub fn new() -> Self {
        Self::default()
    }
}

/// Main entry point for clean command
pub async fn run_clean(
    dry_run: bool, 
    auto: bool, 
    category: Option<String>, 
    config: &Config
) -> Result<()> {
    use crate::scanner::Scanner;
    
    // First scan to get files to clean
    let scanner = Scanner::new(config);
    let scan_results = match category {
        Some(cat) => scanner.scan_category(&cat).await?,
        None => scanner.scan_all(false).await?,
    };
    
    // Filter files based on safety and auto mode
    let files_to_clean = filter_files_for_cleaning(&scan_results, auto, config)?;
    
    if files_to_clean.is_empty() {
        println!("No files found to clean.");
        return Ok(());
    }
    
    // Show what will be cleaned
    display_cleaning_preview(&files_to_clean, dry_run)?;
    
    // Get user confirmation if not in auto mode
    if !auto && !dry_run {
        if !get_user_confirmation()? {
            println!("Cleaning cancelled by user.");
            return Ok(());
        }
    }
    
    // Perform cleaning
    let cleaner = Cleaner::new(config);
    let results = cleaner.clean_files(files_to_clean, dry_run).await?;
    
    // Display results
    display_clean_results(&results)?;
    
    Ok(())
}

fn filter_files_for_cleaning(scan_results: &ScanResults, auto: bool, config: &Config) -> Result<Vec<FileInfo>> {
    let mut files_to_clean = Vec::new();
    
    for (category_name, category_result) in &scan_results.categories {
        // Check if category is enabled for auto cleaning
        let can_auto_clean = match category_name.as_str() {
            "User Cache" | "System Cache" | "Temporary Files" => config.categories.cache.auto_clean,
            "System Logs" => config.categories.logs.auto_clean,
            _ => false,
        };
        
        if auto && !can_auto_clean {
            continue;
        }
        
        for file in &category_result.files {
            if file.safe_to_delete {
                files_to_clean.push(file.clone());
            }
        }
    }
    
    Ok(files_to_clean)
}

fn display_cleaning_preview(files: &[FileInfo], dry_run: bool) -> Result<()> {
    use bytesize::ByteSize;
    
    let total_size: u64 = files.iter().map(|f| f.size).sum();
    let total_files = files.len();
    
    println!("\n🧹 Cleaning Preview");
    println!("==================");
    
    if dry_run {
        println!("DRY RUN MODE - No files will actually be deleted");
    }
    
    println!("Files to clean: {}", total_files);
    println!("Space to free: {}", ByteSize(total_size));
    
    // Group by category
    let mut categories: std::collections::HashMap<String, (usize, u64)> = std::collections::HashMap::new();
    for file in files {
        let entry = categories.entry(file.category.clone()).or_insert((0, 0));
        entry.0 += 1;
        entry.1 += file.size;
    }
    
    println!("\nBy category:");
    for (category, (count, size)) in categories {
        println!("  {} - {} files, {}", category, count, ByteSize(size));
    }
    
    Ok(())
}

fn get_user_confirmation() -> Result<bool> {
    use std::io::{self, Write};
    
    print!("\nProceed with cleaning? [y/N]: ");
    io::stdout().flush()?;
    
    let mut input = String::new();
    io::stdin().read_line(&mut input)?;
    
    Ok(input.trim().to_lowercase() == "y" || input.trim().to_lowercase() == "yes")
}

fn display_clean_results(results: &CleanResults) -> Result<()> {
    use bytesize::ByteSize;
    
    println!("\n✅ Cleaning Results");
    println!("==================");
    
    if results.is_dry_run {
        println!("DRY RUN COMPLETED");
        println!("Would have deleted: {} files", results.deleted_files);
        println!("Would have freed: {}", ByteSize(results.freed_space));
    } else {
        println!("Successfully deleted: {} files", results.deleted_files);
        println!("Space freed: {}", ByteSize(results.freed_space));
        
        if !results.failed_deletions.is_empty() {
            println!("\nFailed deletions: {}", results.failed_deletions.len());
            for (path, error) in &results.failed_deletions {
                println!("  {} - {}", path.display(), error);
            }
        }
        
        if let Some(backup_id) = &results.backup_id {
            println!("\nBackup created: {}", backup_id);
            println!("Use 'macbook-cleaner restore --file {}' to restore if needed", backup_id);
        }
    }
    
    Ok(())
}

/// List available backups
pub fn list_backups(config: &Config) -> Result<()> {
    let backup_manager = BackupManager::new(config);
    backup_manager.list_backups()
}

/// Restore from backup
pub fn restore_backup(backup_id: &str, config: &Config) -> Result<()> {
    let backup_manager = BackupManager::new(config);
    backup_manager.restore_backup(backup_id)
}
