use anyhow::Result;
use crate::config::Config;

pub mod size_analyzer;
pub mod duplicate_finder;

use size_analyzer::<PERSON>zeAnalyzer;
use duplicate_finder::DuplicateFinder;

/// Main analyzer orchestrator
pub struct Analyzer {
    size_analyzer: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    duplicate_finder: DuplicateFinder,
}

impl Analyzer {
    pub fn new(config: &Config) -> Self {
        Self {
            size_analyzer: SizeAnalyzer::new(config),
            duplicate_finder: DuplicateFinder::new(config),
        }
    }
    
    pub async fn analyze_path(&self, path: &str, find_duplicates: bool, find_large_files: bool) -> Result<AnalysisResults> {
        let mut results = AnalysisResults::new();
        
        // Always perform basic size analysis
        let size_results = self.size_analyzer.analyze_directory(path).await?;
        results.merge_size_results(size_results);
        
        // Find large files if requested
        if find_large_files {
            let large_files = self.size_analyzer.find_large_files(path).await?;
            results.large_files = large_files;
        }
        
        // Find duplicates if requested
        if find_duplicates {
            let duplicates = self.duplicate_finder.find_duplicates(path).await?;
            results.duplicate_groups = duplicates;
        }
        
        Ok(results)
    }
}

#[derive(Debug, Default)]
pub struct AnalysisResults {
    pub total_size: u64,
    pub file_count: usize,
    pub directory_count: usize,
    pub size_breakdown: std::collections::HashMap<String, u64>,
    pub large_files: Vec<LargeFileInfo>,
    pub duplicate_groups: Vec<DuplicateGroup>,
}

#[derive(Debug, Clone)]
pub struct LargeFileInfo {
    pub path: std::path::PathBuf,
    pub size: u64,
    pub file_type: String,
}

#[derive(Debug, Clone)]
pub struct DuplicateGroup {
    pub files: Vec<std::path::PathBuf>,
    pub size: u64,
    pub hash: String,
}

impl AnalysisResults {
    pub fn new() -> Self {
        Self::default()
    }
    
    pub fn merge_size_results(&mut self, size_results: SizeAnalysisResults) {
        self.total_size = size_results.total_size;
        self.file_count = size_results.file_count;
        self.directory_count = size_results.directory_count;
        self.size_breakdown = size_results.size_breakdown;
    }
}

#[derive(Debug, Default)]
pub struct SizeAnalysisResults {
    pub total_size: u64,
    pub file_count: usize,
    pub directory_count: usize,
    pub size_breakdown: std::collections::HashMap<String, u64>,
}

/// Main entry point for analyze command
pub async fn run_analysis(
    path: &str,
    duplicates: bool,
    large_files: bool,
    config: &Config,
) -> Result<()> {
    let analyzer = Analyzer::new(config);
    let results = analyzer.analyze_path(path, duplicates, large_files).await?;
    
    // Display results
    display_analysis_results(&results, path)?;
    
    Ok(())
}

fn display_analysis_results(results: &AnalysisResults, path: &str) -> Result<()> {
    use bytesize::ByteSize;
    
    println!("\n📊 Analysis Results for: {}", path);
    println!("=====================================");
    
    println!("\n📁 Directory Summary:");
    println!("  Total size: {}", ByteSize(results.total_size));
    println!("  Files: {}", results.file_count);
    println!("  Directories: {}", results.directory_count);
    
    // Show size breakdown by file type
    if !results.size_breakdown.is_empty() {
        println!("\n📈 Size by File Type:");
        let mut breakdown: Vec<_> = results.size_breakdown.iter().collect();
        breakdown.sort_by(|a, b| b.1.cmp(a.1)); // Sort by size descending
        
        for (file_type, size) in breakdown.iter().take(10) {
            let percentage = (**size as f64 / results.total_size as f64) * 100.0;
            println!("  {} - {} ({:.1}%)", file_type, ByteSize(**size), percentage);
        }
    }
    
    // Show large files
    if !results.large_files.is_empty() {
        println!("\n🔍 Large Files:");
        let mut large_files = results.large_files.clone();
        large_files.sort_by(|a, b| b.size.cmp(&a.size));
        
        for file in large_files.iter().take(20) {
            println!("  {} - {} ({})", 
                    file.path.display(), 
                    ByteSize(file.size),
                    file.file_type);
        }
    }
    
    // Show duplicate groups
    if !results.duplicate_groups.is_empty() {
        println!("\n🔄 Duplicate Files:");
        let mut total_duplicate_size = 0u64;
        let mut total_duplicate_files = 0usize;
        
        for group in &results.duplicate_groups {
            if group.files.len() > 1 {
                let wasted_space = group.size * (group.files.len() - 1) as u64;
                total_duplicate_size += wasted_space;
                total_duplicate_files += group.files.len() - 1;
                
                println!("\n  Group ({}): {} files, {} each", 
                        &group.hash[..8], 
                        group.files.len(), 
                        ByteSize(group.size));
                
                for file in &group.files {
                    println!("    {}", file.display());
                }
            }
        }
        
        if total_duplicate_files > 0 {
            println!("\n  Summary:");
            println!("    Duplicate files: {}", total_duplicate_files);
            println!("    Wasted space: {}", ByteSize(total_duplicate_size));
        }
    }
    
    Ok(())
}
