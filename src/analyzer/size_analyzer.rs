use anyhow::Result;
use std::path::{Path, PathBuf};
use walkdir::WalkDir;
use crate::config::Config;
use super::{SizeAnalysisResults, LargeFileInfo};

pub struct SizeAnalyzer {
    config: Config,
}

impl SizeAnalyzer {
    pub fn new(config: &Config) -> Self {
        Self {
            config: config.clone(),
        }
    }
    
    pub async fn analyze_directory(&self, path: &str) -> Result<SizeAnalysisResults> {
        let path = Path::new(path);
        
        if !path.exists() {
            return Err(anyhow::anyhow!("Path does not exist: {}", path.display()));
        }
        
        let mut results = SizeAnalysisResults::default();
        let mut size_breakdown: std::collections::HashMap<String, u64> = std::collections::HashMap::new();
        
        for entry in WalkDir::new(path)
            .follow_links(false)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_file() {
                if let Ok(metadata) = entry.metadata() {
                    let size = metadata.len();
                    results.total_size += size;
                    results.file_count += 1;
                    
                    // Categorize by file extension
                    let file_type = self.get_file_type(entry.path());
                    *size_breakdown.entry(file_type).or_insert(0) += size;
                }
            } else if entry.file_type().is_dir() {
                results.directory_count += 1;
            }
        }
        
        results.size_breakdown = size_breakdown;
        Ok(results)
    }
    
    pub async fn find_large_files(&self, path: &str) -> Result<Vec<LargeFileInfo>> {
        let path = Path::new(path);
        let threshold = (self.config.thresholds.large_file_size_gb * 1024.0 * 1024.0 * 1024.0) as u64;
        let mut large_files = Vec::new();
        
        for entry in WalkDir::new(path)
            .follow_links(false)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_file() {
                if let Ok(metadata) = entry.metadata() {
                    let size = metadata.len();
                    
                    if size >= threshold {
                        let file_type = self.get_file_type(entry.path());
                        
                        large_files.push(LargeFileInfo {
                            path: entry.path().to_path_buf(),
                            size,
                            file_type,
                        });
                    }
                }
            }
        }
        
        // Sort by size (largest first)
        large_files.sort_by(|a, b| b.size.cmp(&a.size));
        
        Ok(large_files)
    }
    
    fn get_file_type(&self, path: &Path) -> String {
        if let Some(extension) = path.extension().and_then(|ext| ext.to_str()) {
            let ext_lower = extension.to_lowercase();
            
            match ext_lower.as_str() {
                // Images
                "jpg" | "jpeg" | "png" | "gif" | "bmp" | "tiff" | "webp" | "svg" => "Images".to_string(),
                
                // Videos
                "mp4" | "avi" | "mov" | "mkv" | "wmv" | "flv" | "webm" | "m4v" => "Videos".to_string(),
                
                // Audio
                "mp3" | "wav" | "flac" | "aac" | "ogg" | "m4a" | "wma" => "Audio".to_string(),
                
                // Documents
                "pdf" | "doc" | "docx" | "txt" | "rtf" | "odt" => "Documents".to_string(),
                
                // Spreadsheets
                "xls" | "xlsx" | "csv" | "ods" => "Spreadsheets".to_string(),
                
                // Presentations
                "ppt" | "pptx" | "odp" | "key" => "Presentations".to_string(),
                
                // Archives
                "zip" | "rar" | "7z" | "tar" | "gz" | "bz2" | "xz" => "Archives".to_string(),
                
                // Code
                "rs" | "py" | "js" | "ts" | "java" | "cpp" | "c" | "h" | "swift" | "go" => "Code".to_string(),
                
                // Web
                "html" | "css" | "json" | "xml" | "yaml" | "yml" => "Web".to_string(),
                
                // Executables
                "exe" | "app" | "pkg" | "deb" | "rpm" => "Executables".to_string(),

                // Disk Images
                "iso" | "img" | "dmg" => "Disk Images".to_string(),
                
                // Databases
                "db" | "sqlite" | "sql" => "Databases".to_string(),
                
                // Logs
                "log" => "Logs".to_string(),
                
                // Cache
                "cache" | "tmp" => "Cache".to_string(),
                
                _ => format!(".{}", ext_lower),
            }
        } else {
            "No Extension".to_string()
        }
    }
    
    /// Analyze disk usage by directory depth
    pub async fn analyze_directory_tree(&self, path: &str, max_depth: usize) -> Result<Vec<DirectoryInfo>> {
        let path = Path::new(path);
        let mut directories = Vec::new();
        
        for entry in WalkDir::new(path)
            .max_depth(max_depth)
            .follow_links(false)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_dir() {
                let dir_size = self.calculate_directory_size(entry.path())?;
                let file_count = self.count_files_in_directory(entry.path())?;
                
                directories.push(DirectoryInfo {
                    path: entry.path().to_path_buf(),
                    size: dir_size,
                    file_count,
                    depth: entry.depth(),
                });
            }
        }
        
        // Sort by size (largest first)
        directories.sort_by(|a, b| b.size.cmp(&a.size));
        
        Ok(directories)
    }
    
    fn calculate_directory_size(&self, dir_path: &Path) -> Result<u64> {
        let mut total_size = 0;
        
        for entry in WalkDir::new(dir_path)
            .follow_links(false)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_file() {
                if let Ok(metadata) = entry.metadata() {
                    total_size += metadata.len();
                }
            }
        }
        
        Ok(total_size)
    }
    
    fn count_files_in_directory(&self, dir_path: &Path) -> Result<usize> {
        let mut file_count = 0;
        
        for entry in WalkDir::new(dir_path)
            .follow_links(false)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_file() {
                file_count += 1;
            }
        }
        
        Ok(file_count)
    }
    
    /// Find the largest directories within a path
    pub async fn find_largest_directories(&self, path: &str, limit: usize) -> Result<Vec<DirectoryInfo>> {
        let directories = self.analyze_directory_tree(path, 3).await?;
        
        Ok(directories.into_iter().take(limit).collect())
    }
    
    /// Get file size distribution statistics
    pub async fn get_size_distribution(&self, path: &str) -> Result<SizeDistribution> {
        let path = Path::new(path);
        let mut sizes = Vec::new();
        
        for entry in WalkDir::new(path)
            .follow_links(false)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_file() {
                if let Ok(metadata) = entry.metadata() {
                    sizes.push(metadata.len());
                }
            }
        }
        
        if sizes.is_empty() {
            return Ok(SizeDistribution::default());
        }
        
        sizes.sort_unstable();
        
        let total: u64 = sizes.iter().sum();
        let count = sizes.len();
        let mean = total / count as u64;
        let median = sizes[count / 2];
        let min = sizes[0];
        let max = sizes[count - 1];
        
        Ok(SizeDistribution {
            total,
            count,
            mean,
            median,
            min,
            max,
        })
    }
}

#[derive(Debug, Clone)]
pub struct DirectoryInfo {
    pub path: PathBuf,
    pub size: u64,
    pub file_count: usize,
    pub depth: usize,
}

#[derive(Debug, Default)]
pub struct SizeDistribution {
    pub total: u64,
    pub count: usize,
    pub mean: u64,
    pub median: u64,
    pub min: u64,
    pub max: u64,
}
