use anyhow::Result;
use std::path::{Path, PathBuf};
use std::collections::HashMap;
use walkdir::WalkDir;
use sha2::{Sha256, Digest};
use std::fs::File;
use std::io::Read;
use crate::config::Config;
use super::DuplicateGroup;

pub struct DuplicateFinder {
    config: Config,
}

impl DuplicateFinder {
    pub fn new(config: &Config) -> Self {
        Self {
            config: config.clone(),
        }
    }
    
    pub async fn find_duplicates(&self, path: &str) -> Result<Vec<DuplicateGroup>> {
        let path = Path::new(path);
        let min_size = (self.config.thresholds.duplicate_min_size_mb * 1024.0 * 1024.0) as u64;
        
        // First pass: group files by size
        let size_groups = self.group_files_by_size(path, min_size)?;
        
        // Second pass: hash files with same size
        let mut duplicate_groups = Vec::new();
        
        for (size, files) in size_groups {
            if files.len() > 1 {
                let hash_groups = self.group_files_by_hash(&files).await?;
                
                for (hash, duplicate_files) in hash_groups {
                    if duplicate_files.len() > 1 {
                        duplicate_groups.push(DuplicateGroup {
                            files: duplicate_files,
                            size,
                            hash,
                        });
                    }
                }
            }
        }
        
        // Sort by potential space savings (size * (count - 1))
        duplicate_groups.sort_by(|a, b| {
            let savings_a = a.size * (a.files.len() - 1) as u64;
            let savings_b = b.size * (b.files.len() - 1) as u64;
            savings_b.cmp(&savings_a)
        });
        
        Ok(duplicate_groups)
    }
    
    fn group_files_by_size(&self, path: &Path, min_size: u64) -> Result<HashMap<u64, Vec<PathBuf>>> {
        let mut size_groups: HashMap<u64, Vec<PathBuf>> = HashMap::new();
        
        for entry in WalkDir::new(path)
            .follow_links(false)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_file() {
                if let Ok(metadata) = entry.metadata() {
                    let size = metadata.len();
                    
                    // Only consider files above minimum size threshold
                    if size >= min_size {
                        size_groups
                            .entry(size)
                            .or_insert_with(Vec::new)
                            .push(entry.path().to_path_buf());
                    }
                }
            }
        }
        
        // Remove size groups with only one file
        size_groups.retain(|_, files| files.len() > 1);
        
        Ok(size_groups)
    }
    
    async fn group_files_by_hash(&self, files: &[PathBuf]) -> Result<HashMap<String, Vec<PathBuf>>> {
        let mut hash_groups: HashMap<String, Vec<PathBuf>> = HashMap::new();
        
        for file_path in files {
            match self.calculate_file_hash(file_path).await {
                Ok(hash) => {
                    hash_groups
                        .entry(hash)
                        .or_insert_with(Vec::new)
                        .push(file_path.clone());
                },
                Err(e) => {
                    log::warn!("Failed to hash file {}: {}", file_path.display(), e);
                }
            }
        }
        
        Ok(hash_groups)
    }
    
    async fn calculate_file_hash(&self, file_path: &Path) -> Result<String> {
        let mut file = File::open(file_path)?;
        let mut hasher = Sha256::new();
        let mut buffer = [0; 8192];
        
        loop {
            let bytes_read = file.read(&mut buffer)?;
            if bytes_read == 0 {
                break;
            }
            hasher.update(&buffer[..bytes_read]);
        }
        
        Ok(format!("{:x}", hasher.finalize()))
    }
    
    /// Find duplicates using a faster method for large directories
    pub async fn find_duplicates_fast(&self, path: &str) -> Result<Vec<DuplicateGroup>> {
        let path = Path::new(path);
        let min_size = (self.config.thresholds.duplicate_min_size_mb * 1024.0 * 1024.0) as u64;
        
        // First pass: group by size
        let size_groups = self.group_files_by_size(path, min_size)?;
        
        // Second pass: use partial hashing for speed
        let mut duplicate_groups = Vec::new();
        
        for (size, files) in size_groups {
            if files.len() > 1 {
                // First hash only the first 1KB of each file
                let partial_hash_groups = self.group_files_by_partial_hash(&files, 1024).await?;
                
                // Then do full hash only for files with matching partial hashes
                for (_, partial_matches) in partial_hash_groups {
                    if partial_matches.len() > 1 {
                        let full_hash_groups = self.group_files_by_hash(&partial_matches).await?;
                        
                        for (hash, duplicate_files) in full_hash_groups {
                            if duplicate_files.len() > 1 {
                                duplicate_groups.push(DuplicateGroup {
                                    files: duplicate_files,
                                    size,
                                    hash,
                                });
                            }
                        }
                    }
                }
            }
        }
        
        Ok(duplicate_groups)
    }
    
    async fn group_files_by_partial_hash(&self, files: &[PathBuf], bytes_to_read: usize) -> Result<HashMap<String, Vec<PathBuf>>> {
        let mut hash_groups: HashMap<String, Vec<PathBuf>> = HashMap::new();
        
        for file_path in files {
            match self.calculate_partial_file_hash(file_path, bytes_to_read).await {
                Ok(hash) => {
                    hash_groups
                        .entry(hash)
                        .or_insert_with(Vec::new)
                        .push(file_path.clone());
                },
                Err(e) => {
                    log::warn!("Failed to partially hash file {}: {}", file_path.display(), e);
                }
            }
        }
        
        Ok(hash_groups)
    }
    
    async fn calculate_partial_file_hash(&self, file_path: &Path, bytes_to_read: usize) -> Result<String> {
        let mut file = File::open(file_path)?;
        let mut hasher = Sha256::new();
        let mut buffer = vec![0; bytes_to_read];
        
        let bytes_read = file.read(&mut buffer)?;
        hasher.update(&buffer[..bytes_read]);
        
        Ok(format!("{:x}", hasher.finalize()))
    }
    
    /// Find duplicate files by name (not content)
    pub async fn find_name_duplicates(&self, path: &str) -> Result<Vec<NameDuplicateGroup>> {
        let path = Path::new(path);
        let mut name_groups: HashMap<String, Vec<PathBuf>> = HashMap::new();
        
        for entry in WalkDir::new(path)
            .follow_links(false)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_file() {
                if let Some(file_name) = entry.file_name().to_str() {
                    name_groups
                        .entry(file_name.to_string())
                        .or_insert_with(Vec::new)
                        .push(entry.path().to_path_buf());
                }
            }
        }
        
        let mut duplicate_groups = Vec::new();
        
        for (name, files) in name_groups {
            if files.len() > 1 {
                let total_size: u64 = files.iter()
                    .filter_map(|f| std::fs::metadata(f).ok())
                    .map(|m| m.len())
                    .sum();
                
                duplicate_groups.push(NameDuplicateGroup {
                    name,
                    files,
                    total_size,
                });
            }
        }
        
        // Sort by total size
        duplicate_groups.sort_by(|a, b| b.total_size.cmp(&a.total_size));
        
        Ok(duplicate_groups)
    }
    
    /// Calculate potential space savings from duplicates
    pub fn calculate_savings(&self, duplicate_groups: &[DuplicateGroup]) -> DuplicateSavings {
        let mut total_files = 0;
        let mut total_savings = 0u64;
        let mut largest_group_size = 0u64;
        let mut largest_group_count = 0;
        
        for group in duplicate_groups {
            let duplicates = group.files.len() - 1; // Keep one copy
            total_files += duplicates;
            total_savings += group.size * duplicates as u64;
            
            if group.files.len() > largest_group_count {
                largest_group_count = group.files.len();
                largest_group_size = group.size;
            }
        }
        
        DuplicateSavings {
            total_duplicate_files: total_files,
            total_savings_bytes: total_savings,
            duplicate_groups: duplicate_groups.len(),
            largest_group_size,
            largest_group_count,
        }
    }
}

#[derive(Debug, Clone)]
pub struct NameDuplicateGroup {
    pub name: String,
    pub files: Vec<PathBuf>,
    pub total_size: u64,
}

#[derive(Debug)]
pub struct DuplicateSavings {
    pub total_duplicate_files: usize,
    pub total_savings_bytes: u64,
    pub duplicate_groups: usize,
    pub largest_group_size: u64,
    pub largest_group_count: usize,
}
