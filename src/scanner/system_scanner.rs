use anyhow::Result;
use std::path::{Path, PathBuf};
use walkdir::WalkDir;
use crate::config::Config;
use super::{ScanResults, CategoryResult, FileInfo};

pub struct SystemScanner {
    config: Config,
}

impl SystemScanner {
    pub fn new(config: &Config) -> Self {
        Self {
            config: config.clone(),
        }
    }
    
    pub async fn scan(&self) -> Result<ScanResults> {
        let mut results = ScanResults::new();
        
        // Scan system logs
        self.scan_system_logs(&mut results).await?;
        
        // Scan language files (localizations)
        self.scan_language_files(&mut results).await?;
        
        // Scan for large files
        self.scan_large_files(&mut results).await?;
        
        Ok(results)
    }
    
    async fn scan_system_logs(&self, results: &mut ScanResults) -> Result<()> {
        let log_paths = vec![
            PathBuf::from("/var/log"),
            PathBuf::from("/Library/Logs"),
        ];
        
        for log_path in log_paths {
            if log_path.exists() {
                // Only scan if we have read permissions
                if let Ok(_) = std::fs::read_dir(&log_path) {
                    self.scan_directory_with_age_filter(&log_path, "System Logs", results, 30).await?;
                }
            }
        }
        
        Ok(())
    }
    
    async fn scan_language_files(&self, results: &mut ScanResults) -> Result<()> {
        // Scan for .lproj directories (localization files)
        let search_paths = vec![
            PathBuf::from("/Applications"),
            PathBuf::from("/System/Library"),
        ];
        
        for search_path in search_paths {
            if search_path.exists() {
                self.scan_for_localizations(&search_path, results).await?;
            }
        }
        
        Ok(())
    }
    
    async fn scan_large_files(&self, results: &mut ScanResults) -> Result<()> {
        let home_dir = dirs::home_dir().ok_or_else(|| anyhow::anyhow!("Could not find home directory"))?;
        
        // Scan common locations for large files
        let search_paths = vec![
            home_dir.clone(),
            PathBuf::from("/Applications"),
        ];
        
        let large_file_threshold = (self.config.thresholds.large_file_size_gb * 1024.0 * 1024.0 * 1024.0) as u64;
        
        for search_path in search_paths {
            if search_path.exists() {
                self.scan_for_large_files(&search_path, large_file_threshold, results).await?;
            }
        }
        
        Ok(())
    }
    
    async fn scan_directory_with_age_filter(
        &self, 
        dir_path: &Path, 
        category: &str, 
        results: &mut ScanResults,
        max_age_days: u64
    ) -> Result<()> {
        let mut category_result = CategoryResult::default();
        let max_age = std::time::Duration::from_secs(max_age_days * 24 * 3600);
        
        for entry in WalkDir::new(dir_path)
            .follow_links(false)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_file() {
                if let Ok(metadata) = entry.metadata() {
                    let size = metadata.len();
                    let modified = metadata.modified().unwrap_or(std::time::UNIX_EPOCH);
                    
                    // Check file age
                    let age = std::time::SystemTime::now()
                        .duration_since(modified)
                        .unwrap_or_default();
                    
                    let safe_to_delete = age > max_age && self.is_safe_to_delete(entry.path())?;
                    
                    let file_info = FileInfo {
                        path: entry.path().to_path_buf(),
                        size,
                        modified,
                        category: category.to_string(),
                        safe_to_delete,
                        description: format!("System log file ({})", self.format_age(age)),
                    };
                    
                    category_result.files.push(file_info);
                    category_result.size += size;
                    category_result.count += 1;
                    
                    results.total_size += size;
                    results.file_count += 1;
                }
            }
        }
        
        if !category_result.files.is_empty() {
            results.categories.insert(category.to_string(), category_result);
        }
        
        Ok(())
    }
    
    async fn scan_for_localizations(&self, base_path: &Path, results: &mut ScanResults) -> Result<()> {
        let mut category_result = CategoryResult::default();
        
        for entry in WalkDir::new(base_path)
            .follow_links(false)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_dir() {
                if let Some(dir_name) = entry.file_name().to_str() {
                    if dir_name.ends_with(".lproj") && dir_name != "en.lproj" && dir_name != "Base.lproj" {
                        // This is a non-English localization directory
                        let dir_size = self.calculate_directory_size(entry.path())?;
                        
                        let file_info = FileInfo {
                            path: entry.path().to_path_buf(),
                            size: dir_size,
                            modified: entry.metadata()?.modified().unwrap_or(std::time::UNIX_EPOCH),
                            category: "Language Files".to_string(),
                            safe_to_delete: true, // Generally safe to remove unused localizations
                            description: format!("Localization files for {}", dir_name.replace(".lproj", "")),
                        };
                        
                        category_result.files.push(file_info);
                        category_result.size += dir_size;
                        category_result.count += 1;
                        
                        results.total_size += dir_size;
                        results.file_count += 1;
                    }
                }
            }
        }
        
        if !category_result.files.is_empty() {
            results.categories.insert("Language Files".to_string(), category_result);
        }
        
        Ok(())
    }
    
    async fn scan_for_large_files(&self, base_path: &Path, threshold: u64, results: &mut ScanResults) -> Result<()> {
        let mut category_result = CategoryResult::default();
        
        for entry in WalkDir::new(base_path)
            .follow_links(false)
            .max_depth(3) // Limit depth to avoid scanning too deep
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_file() {
                if let Ok(metadata) = entry.metadata() {
                    let size = metadata.len();
                    
                    if size >= threshold {
                        let modified = metadata.modified().unwrap_or(std::time::UNIX_EPOCH);
                        
                        let file_info = FileInfo {
                            path: entry.path().to_path_buf(),
                            size,
                            modified,
                            category: "Large Files".to_string(),
                            safe_to_delete: false, // Large files require manual review
                            description: format!("Large file ({})", bytesize::ByteSize(size)),
                        };
                        
                        category_result.files.push(file_info);
                        category_result.size += size;
                        category_result.count += 1;
                        
                        results.total_size += size;
                        results.file_count += 1;
                    }
                }
            }
        }
        
        if !category_result.files.is_empty() {
            results.categories.insert("Large Files".to_string(), category_result);
        }
        
        Ok(())
    }
    
    fn calculate_directory_size(&self, dir_path: &Path) -> Result<u64> {
        let mut total_size = 0;
        
        for entry in WalkDir::new(dir_path)
            .follow_links(false)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_file() {
                if let Ok(metadata) = entry.metadata() {
                    total_size += metadata.len();
                }
            }
        }
        
        Ok(total_size)
    }
    
    fn is_safe_to_delete(&self, path: &Path) -> Result<bool> {
        // Check if file is in protected paths
        for protected_path in &self.config.safety.protected_paths {
            if path.starts_with(protected_path) {
                return Ok(false);
            }
        }
        
        // Check for critical system files
        let path_str = path.to_string_lossy().to_lowercase();
        let critical_patterns = vec![
            "kernel",
            "boot",
            "system",
            "library/coreservices",
            "usr/bin",
            "usr/sbin",
        ];
        
        for pattern in critical_patterns {
            if path_str.contains(pattern) {
                return Ok(false);
            }
        }
        
        Ok(true)
    }
    
    fn format_age(&self, age: std::time::Duration) -> String {
        let days = age.as_secs() / (24 * 3600);
        if days == 0 {
            "today".to_string()
        } else if days == 1 {
            "1 day old".to_string()
        } else {
            format!("{} days old", days)
        }
    }
}
