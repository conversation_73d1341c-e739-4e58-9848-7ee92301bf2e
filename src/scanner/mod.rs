use anyhow::Result;
use crate::config::Config;

pub mod cache_scanner;
pub mod app_scanner;
pub mod user_scanner;
pub mod system_scanner;

use cache_scanner::CacheScanner;
use app_scanner::AppScanner;
use user_scanner::UserScanner;
use system_scanner::SystemScanner;

/// Main scanner orchestrator
pub struct Scanner {
    cache_scanner: CacheScanner,
    app_scanner: AppScanner,
    user_scanner: UserScanner,
    system_scanner: SystemScanner,
}

impl Scanner {
    pub fn new(config: &Config) -> Self {
        Self {
            cache_scanner: CacheScanner::new(config),
            app_scanner: AppScanner::new(config),
            user_scanner: UserScanner::new(config),
            system_scanner: SystemScanner::new(config),
        }
    }
    
    pub async fn scan_all(&self, deep: bool) -> Result<ScanResults> {
        let mut results = ScanResults::new();

        let (cache_res, app_res, system_res) = tokio::join!(
            self.cache_scanner.scan(),
            self.app_scanner.scan(),
            self.system_scanner.scan()
        );

        results.merge(cache_res?);
        results.merge(app_res?);
        results.merge(system_res?);

        // Scan user data (if enabled) - this can be large and might benefit from separate handling
        if deep {
            let user_results = self.user_scanner.scan().await?;
            results.merge(user_results);
        }
        
        Ok(results)
    }
    
    pub async fn scan_category(&self, category: &str) -> Result<ScanResults> {
        match category.to_lowercase().as_str() {
            "cache" => self.cache_scanner.scan().await,
            "apps" | "applications" => self.app_scanner.scan().await,
            "user" => self.user_scanner.scan().await,
            "system" => self.system_scanner.scan().await,
            _ => Err(anyhow::anyhow!("Unknown category: {}", category)),
        }
    }
}

#[derive(Debug, Default)]
pub struct ScanResults {
    pub total_size: u64,
    pub file_count: usize,
    pub categories: std::collections::HashMap<String, CategoryResult>,
}

#[derive(Debug, Default)]
pub struct CategoryResult {
    pub size: u64,
    pub count: usize,
    pub files: Vec<FileInfo>,
}

#[derive(Debug, Clone)]
pub struct FileInfo {
    pub path: std::path::PathBuf,
    pub size: u64,
    pub modified: std::time::SystemTime,
    pub category: String,
    pub safe_to_delete: bool,
    pub description: String,
}

impl ScanResults {
    pub fn new() -> Self {
        Self::default()
    }
    
    pub fn merge(&mut self, other: ScanResults) {
        self.total_size += other.total_size;
        self.file_count += other.file_count;
        
        for (category, mut result) in other.categories {
            self.categories.entry(category)
                .and_modify(|existing| {
                    existing.size += result.size;
                    existing.count += result.count;
                    existing.files.append(&mut result.files);
                })
                .or_insert(result);
        }
    }
}

/// Main entry point for scan command
pub async fn run_scan(deep: bool, category: Option<String>, config: &Config) -> Result<()> {
    let scanner = Scanner::new(config);
    
    let results = match category {
        Some(cat) => scanner.scan_category(&cat).await?,
        None => scanner.scan_all(deep).await?,
    };
    
    // Display results
    display_scan_results(&results)?;
    
    Ok(())
}

fn display_scan_results(results: &ScanResults) -> Result<()> {
    use bytesize::ByteSize;
    
    println!("\n📊 Scan Results");
    println!("================");
    println!("Total files found: {}", results.file_count);
    println!("Total size: {}", ByteSize(results.total_size));
    
    println!("\n📁 By Category:");
    for (category, result) in &results.categories {
        println!("  {} - {} files, {}", 
                category, 
                result.count, 
                ByteSize(result.size));
    }
    
    Ok(())
}
