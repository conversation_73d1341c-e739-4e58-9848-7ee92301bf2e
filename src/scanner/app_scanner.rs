use anyhow::Result;
use std::path::{Path, PathBuf};
use walkdir::WalkDir;
use crate::config::Config;
use super::{ScanResults, CategoryResult, FileInfo};

pub struct AppScanner {
    config: Config,
}

impl AppScanner {
    pub fn new(config: &Config) -> Self {
        Self {
            config: config.clone(),
        }
    }
    
    pub async fn scan(&self) -> Result<ScanResults> {
        let mut results = ScanResults::new();
        
        // Scan Xcode-related files
        self.scan_xcode_files(&mut results).await?;
        
        // Scan Node.js projects
        self.scan_nodejs_files(&mut results).await?;
        
        // Scan Python files
        self.scan_python_files(&mut results).await?;
        
        // Scan Docker files
        self.scan_docker_files(&mut results).await?;
        
        // Scan Homebrew cache
        self.scan_homebrew_cache(&mut results).await?;
        
        Ok(results)
    }
    
    async fn scan_xcode_files(&self, results: &mut ScanResults) -> Result<()> {
        let home_dir = dirs::home_dir().ok_or_else(|| anyhow::anyhow!("Could not find home directory"))?;
        
        let xcode_paths = vec![
            home_dir.join("Library/Developer/Xcode/DerivedData"),
            home_dir.join("Library/Developer/Xcode/Archives"),
            home_dir.join("Library/Developer/Xcode/iOS DeviceSupport"),
        ];
        
        for xcode_path in xcode_paths {
            if xcode_path.exists() {
                self.scan_directory(&xcode_path, "Xcode", results).await?;
            }
        }
        
        Ok(())
    }
    
    async fn scan_nodejs_files(&self, results: &mut ScanResults) -> Result<()> {
        let home_dir = dirs::home_dir().ok_or_else(|| anyhow::anyhow!("Could not find home directory"))?;
        
        // Scan for node_modules directories
        self.scan_for_pattern(&home_dir, "node_modules", "Node.js", results).await?;
        
        // Scan npm cache
        if let Some(npm_cache) = self.get_npm_cache_dir() {
            if npm_cache.exists() {
                self.scan_directory(&npm_cache, "NPM Cache", results).await?;
            }
        }
        
        Ok(())
    }
    
    async fn scan_python_files(&self, results: &mut ScanResults) -> Result<()> {
        let home_dir = dirs::home_dir().ok_or_else(|| anyhow::anyhow!("Could not find home directory"))?;
        
        // Scan for __pycache__ directories
        self.scan_for_pattern(&home_dir, "__pycache__", "Python Cache", results).await?;
        
        // Scan for .pyc files
        self.scan_for_extension(&home_dir, "pyc", "Python Bytecode", results).await?;
        
        Ok(())
    }
    
    async fn scan_docker_files(&self, results: &mut ScanResults) -> Result<()> {
        // Docker data is typically in /var/lib/docker but requires root access
        // We'll focus on user-accessible Docker cache locations
        let home_dir = dirs::home_dir().ok_or_else(|| anyhow::anyhow!("Could not find home directory"))?;
        
        let docker_cache = home_dir.join("Library/Containers/com.docker.docker/Data");
        if docker_cache.exists() {
            self.scan_directory(&docker_cache, "Docker", results).await?;
        }
        
        Ok(())
    }
    
    async fn scan_homebrew_cache(&self, results: &mut ScanResults) -> Result<()> {
        let homebrew_cache_paths = vec![
            PathBuf::from("/usr/local/var/homebrew/cache"),
            PathBuf::from("/opt/homebrew/var/homebrew/cache"),
        ];
        
        for cache_path in homebrew_cache_paths {
            if cache_path.exists() {
                self.scan_directory(&cache_path, "Homebrew Cache", results).await?;
            }
        }
        
        Ok(())
    }
    
    async fn scan_directory(&self, dir_path: &Path, category: &str, results: &mut ScanResults) -> Result<()> {
        let mut category_result = CategoryResult::default();
        
        for entry in WalkDir::new(dir_path)
            .follow_links(false)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_file() {
                if let Ok(metadata) = entry.metadata() {
                    let size = metadata.len();
                    let modified = metadata.modified().unwrap_or(std::time::UNIX_EPOCH);
                    
                    let safe_to_delete = self.is_safe_to_delete(entry.path(), category)?;
                    
                    let file_info = FileInfo {
                        path: entry.path().to_path_buf(),
                        size,
                        modified,
                        category: category.to_string(),
                        safe_to_delete,
                        description: self.get_file_description(entry.path(), category),
                    };
                    
                    category_result.files.push(file_info);
                    category_result.size += size;
                    category_result.count += 1;
                    
                    results.total_size += size;
                    results.file_count += 1;
                }
            }
        }
        
        if !category_result.files.is_empty() {
            results.categories.insert(category.to_string(), category_result);
        }
        
        Ok(())
    }
    
    async fn scan_for_pattern(&self, base_path: &Path, pattern: &str, category: &str, results: &mut ScanResults) -> Result<()> {
        for entry in WalkDir::new(base_path)
            .follow_links(false)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_dir() && entry.file_name() == pattern {
                self.scan_directory(entry.path(), category, results).await?;
            }
        }
        Ok(())
    }
    
    async fn scan_for_extension(&self, base_path: &Path, extension: &str, category: &str, results: &mut ScanResults) -> Result<()> {
        let mut category_result = CategoryResult::default();
        
        for entry in WalkDir::new(base_path)
            .follow_links(false)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_file() {
                if let Some(ext) = entry.path().extension() {
                    if ext == extension {
                        if let Ok(metadata) = entry.metadata() {
                            let size = metadata.len();
                            let modified = metadata.modified().unwrap_or(std::time::UNIX_EPOCH);
                            
                            let file_info = FileInfo {
                                path: entry.path().to_path_buf(),
                                size,
                                modified,
                                category: category.to_string(),
                                safe_to_delete: true,
                                description: format!("{} file", extension.to_uppercase()),
                            };
                            
                            category_result.files.push(file_info);
                            category_result.size += size;
                            category_result.count += 1;
                            
                            results.total_size += size;
                            results.file_count += 1;
                        }
                    }
                }
            }
        }
        
        if !category_result.files.is_empty() {
            results.categories.insert(category.to_string(), category_result);
        }
        
        Ok(())
    }
    
    fn get_npm_cache_dir(&self) -> Option<PathBuf> {
        if let Ok(output) = std::process::Command::new("npm")
            .args(&["config", "get", "cache"])
            .output()
        {
            if let Ok(cache_path) = String::from_utf8(output.stdout) {
                return Some(PathBuf::from(cache_path.trim()));
            }
        }
        None
    }
    
    fn is_safe_to_delete(&self, path: &Path, category: &str) -> Result<bool> {
        // Check if file is in protected paths
        for protected_path in &self.config.safety.protected_paths {
            if path.starts_with(protected_path) {
                return Ok(false);
            }
        }
        
        // Most app-specific files are safe to delete
        match category {
            "Xcode" | "NPM Cache" | "Python Cache" | "Python Bytecode" | "Homebrew Cache" => Ok(true),
            "Node.js" => {
                // Only delete node_modules in non-active projects
                // This is a simplified check - in practice, we'd want to check for recent activity
                Ok(true)
            },
            "Docker" => Ok(true),
            _ => Ok(false),
        }
    }
    
    fn get_file_description(&self, path: &Path, category: &str) -> String {
        match category {
            "Xcode" => "Xcode build data and archives".to_string(),
            "Node.js" => "Node.js dependencies".to_string(),
            "Python Cache" => "Python compiled bytecode".to_string(),
            "Docker" => "Docker container data".to_string(),
            "Homebrew Cache" => "Homebrew package cache".to_string(),
            _ => format!("{} application file", category),
        }
    }
}
