use anyhow::Result;
use std::path::{Path, PathBuf};
use walkdir::WalkDir;
use crate::config::Config;
use super::{ScanResults, CategoryResult, FileInfo};

pub struct CacheScanner {
    config: Config,
}

impl CacheScanner {
    pub fn new(config: &Config) -> Self {
        Self {
            config: config.clone(),
        }
    }
    
    pub async fn scan(&self) -> Result<ScanResults> {
        let mut results = ScanResults::new();
        
        // Scan user cache directories
        self.scan_user_caches(&mut results).await?;
        
        // Scan system cache directories (if permissions allow)
        self.scan_system_caches(&mut results).await?;
        
        // Scan temporary directories
        self.scan_temp_directories(&mut results).await?;
        
        Ok(results)
    }
    
    async fn scan_user_caches(&self, results: &mut ScanResults) -> Result<()> {
        let home_dir = dirs::home_dir().ok_or_else(|| anyhow::anyhow!("Could not find home directory"))?;
        
        let cache_paths = vec![
            home_dir.join("Library/Caches"),
            home_dir.join("Library/Logs"),
        ];
        
        for cache_path in cache_paths {
            if cache_path.exists() {
                self.scan_directory(&cache_path, "User Cache", results).await?;
            }
        }
        
        Ok(())
    }
    
    async fn scan_system_caches(&self, results: &mut ScanResults) -> Result<()> {
        let system_cache_paths = vec![
            PathBuf::from("/System/Library/Caches"),
            PathBuf::from("/var/log"),
        ];
        
        for cache_path in system_cache_paths {
            if cache_path.exists() {
                // Only scan if we have read permissions
                if let Ok(_) = std::fs::read_dir(&cache_path) {
                    self.scan_directory(&cache_path, "System Cache", results).await?;
                }
            }
        }
        
        Ok(())
    }
    
    async fn scan_temp_directories(&self, results: &mut ScanResults) -> Result<()> {
        let temp_paths = vec![
            PathBuf::from("/tmp"),
            PathBuf::from("/private/var/folders"),
        ];
        
        for temp_path in temp_paths {
            if temp_path.exists() {
                self.scan_directory(&temp_path, "Temporary Files", results).await?;
            }
        }
        
        Ok(())
    }
    
    async fn scan_directory(&self, dir_path: &Path, category: &str, results: &mut ScanResults) -> Result<()> {
        let mut category_result = CategoryResult::default();
        
        for entry in WalkDir::new(dir_path)
            .follow_links(false)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_file() {
                if let Ok(metadata) = entry.metadata() {
                    let size = metadata.len();
                    let modified = metadata.modified().unwrap_or(std::time::UNIX_EPOCH);
                    
                    // Check if file is safe to delete based on age and patterns
                    let safe_to_delete = self.is_safe_to_delete(entry.path(), &metadata)?;
                    
                    let file_info = FileInfo {
                        path: entry.path().to_path_buf(),
                        size,
                        modified,
                        category: category.to_string(),
                        safe_to_delete,
                        description: self.get_file_description(entry.path()),
                    };
                    
                    category_result.files.push(file_info);
                    category_result.size += size;
                    category_result.count += 1;
                    
                    results.total_size += size;
                    results.file_count += 1;
                }
            }
        }
        
        if !category_result.files.is_empty() {
            results.categories.insert(category.to_string(), category_result);
        }
        
        Ok(())
    }
    
    fn is_safe_to_delete(&self, path: &Path, metadata: &std::fs::Metadata) -> Result<bool> {
        // Check if file is in protected paths
        for protected_path in &self.config.safety.protected_paths {
            if path.starts_with(protected_path) {
                return Ok(false);
            }
        }
        
        // Check file age
        if let Ok(modified) = metadata.modified() {
            let age = std::time::SystemTime::now()
                .duration_since(modified)
                .unwrap_or_default();
            
            let max_age = std::time::Duration::from_secs(
                self.config.general.max_file_age_days as u64 * 24 * 3600
            );
            
            if age < max_age {
                return Ok(false);
            }
        }
        
        // Check file patterns that are generally safe to delete
        if let Some(file_name) = path.file_name().and_then(|n| n.to_str()) {
            let safe_patterns = vec![
                ".cache",
                ".tmp",
                ".log",
                "cache",
                "temp",
                "temporary",
            ];
            
            for pattern in safe_patterns {
                if file_name.contains(pattern) {
                    return Ok(true);
                }
            }
        }
        
        Ok(false)
    }
    
    fn get_file_description(&self, path: &Path) -> String {
        if let Some(parent) = path.parent() {
            if let Some(parent_name) = parent.file_name().and_then(|n| n.to_str()) {
                return format!("Cache file from {}", parent_name);
            }
        }
        
        "Cache or temporary file".to_string()
    }
}
