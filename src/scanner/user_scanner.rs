use anyhow::Result;
use std::path::{Path, PathBuf};
use walkdir::WalkDir;
use crate::config::Config;
use super::{ScanResults, CategoryResult, FileInfo};

pub struct UserScanner {
    config: Config,
}

impl UserScanner {
    pub fn new(config: &Config) -> Self {
        Self {
            config: config.clone(),
        }
    }
    
    pub async fn scan(&self) -> Result<ScanResults> {
        let mut results = ScanResults::new();
        
        // Scan Downloads folder for old files
        self.scan_downloads(&mut results).await?;
        
        // Scan Trash
        self.scan_trash(&mut results).await?;
        
        // Scan browser caches
        self.scan_browser_caches(&mut results).await?;
        
        // Scan mail attachments
        self.scan_mail_attachments(&mut results).await?;
        
        Ok(results)
    }
    
    async fn scan_downloads(&self, results: &mut ScanResults) -> Result<()> {
        let home_dir = dirs::home_dir().ok_or_else(|| anyhow::anyhow!("Could not find home directory"))?;
        let downloads_path = home_dir.join("Downloads");
        
        if !downloads_path.exists() {
            return Ok(());
        }
        
        let mut category_result = CategoryResult::default();
        let age_threshold = std::time::Duration::from_secs(
            self.config.categories.downloads.age_threshold as u64 * 24 * 3600
        );
        
        for entry in WalkDir::new(&downloads_path)
            .max_depth(1)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_file() {
                if let Ok(metadata) = entry.metadata() {
                    let size = metadata.len();
                    let modified = metadata.modified().unwrap_or(std::time::UNIX_EPOCH);
                    
                    // Check if file is older than threshold
                    let age = std::time::SystemTime::now()
                        .duration_since(modified)
                        .unwrap_or_default();
                    
                    let safe_to_delete = age > age_threshold;
                    
                    let file_info = FileInfo {
                        path: entry.path().to_path_buf(),
                        size,
                        modified,
                        category: "Downloads".to_string(),
                        safe_to_delete,
                        description: format!("Downloaded file ({})", self.format_age(age)),
                    };
                    
                    category_result.files.push(file_info);
                    category_result.size += size;
                    category_result.count += 1;
                    
                    results.total_size += size;
                    results.file_count += 1;
                }
            }
        }
        
        if !category_result.files.is_empty() {
            results.categories.insert("Downloads".to_string(), category_result);
        }
        
        Ok(())
    }
    
    async fn scan_trash(&self, results: &mut ScanResults) -> Result<()> {
        let home_dir = dirs::home_dir().ok_or_else(|| anyhow::anyhow!("Could not find home directory"))?;
        let trash_path = home_dir.join(".Trash");
        
        if trash_path.exists() {
            self.scan_directory(&trash_path, "Trash", results).await?;
        }
        
        Ok(())
    }
    
    async fn scan_browser_caches(&self, results: &mut ScanResults) -> Result<()> {
        let home_dir = dirs::home_dir().ok_or_else(|| anyhow::anyhow!("Could not find home directory"))?;
        
        let browser_cache_paths = vec![
            // Safari
            home_dir.join("Library/Caches/com.apple.Safari"),
            home_dir.join("Library/Safari/LocalStorage"),
            // Chrome
            home_dir.join("Library/Caches/Google/Chrome"),
            home_dir.join("Library/Application Support/Google/Chrome/Default/Cache"),
            // Firefox
            home_dir.join("Library/Caches/Firefox"),
            home_dir.join("Library/Application Support/Firefox/Profiles"),
        ];
        
        for cache_path in browser_cache_paths {
            if cache_path.exists() {
                let category = self.get_browser_category(&cache_path);
                self.scan_directory(&cache_path, &category, results).await?;
            }
        }
        
        Ok(())
    }
    
    async fn scan_mail_attachments(&self, results: &mut ScanResults) -> Result<()> {
        let home_dir = dirs::home_dir().ok_or_else(|| anyhow::anyhow!("Could not find home directory"))?;
        
        let mail_paths = vec![
            home_dir.join("Library/Mail/V10/MailData/Attachments"),
            home_dir.join("Library/Group Containers/UBF8T346G9.Office/Outlook/Outlook 15 Profiles"),
        ];
        
        for mail_path in mail_paths {
            if mail_path.exists() {
                self.scan_directory(&mail_path, "Mail Attachments", results).await?;
            }
        }
        
        Ok(())
    }
    
    async fn scan_directory(&self, dir_path: &Path, category: &str, results: &mut ScanResults) -> Result<()> {
        let mut category_result = CategoryResult::default();
        
        for entry in WalkDir::new(dir_path)
            .follow_links(false)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_file() {
                if let Ok(metadata) = entry.metadata() {
                    let size = metadata.len();
                    let modified = metadata.modified().unwrap_or(std::time::UNIX_EPOCH);
                    
                    let safe_to_delete = self.is_safe_to_delete(entry.path(), category)?;
                    
                    let file_info = FileInfo {
                        path: entry.path().to_path_buf(),
                        size,
                        modified,
                        category: category.to_string(),
                        safe_to_delete,
                        description: self.get_file_description(entry.path(), category),
                    };
                    
                    category_result.files.push(file_info);
                    category_result.size += size;
                    category_result.count += 1;
                    
                    results.total_size += size;
                    results.file_count += 1;
                }
            }
        }
        
        if !category_result.files.is_empty() {
            results.categories.insert(category.to_string(), category_result);
        }
        
        Ok(())
    }
    
    fn get_browser_category(&self, path: &Path) -> String {
        let path_str = path.to_string_lossy().to_lowercase();
        
        if path_str.contains("safari") {
            "Safari Cache".to_string()
        } else if path_str.contains("chrome") {
            "Chrome Cache".to_string()
        } else if path_str.contains("firefox") {
            "Firefox Cache".to_string()
        } else {
            "Browser Cache".to_string()
        }
    }
    
    fn is_safe_to_delete(&self, path: &Path, category: &str) -> Result<bool> {
        // Check if file is in protected paths
        for protected_path in &self.config.safety.protected_paths {
            if path.starts_with(protected_path) {
                return Ok(false);
            }
        }
        
        match category {
            "Trash" => Ok(true), // Trash files are always safe to delete
            "Safari Cache" | "Chrome Cache" | "Firefox Cache" => Ok(true), // Browser caches are safe
            "Mail Attachments" => {
                // Only delete old mail attachments
                if let Ok(metadata) = std::fs::metadata(path) {
                    if let Ok(modified) = metadata.modified() {
                        let age = std::time::SystemTime::now()
                            .duration_since(modified)
                            .unwrap_or_default();
                        
                        let max_age = std::time::Duration::from_secs(30 * 24 * 3600); // 30 days
                        return Ok(age > max_age);
                    }
                }
                Ok(false)
            },
            "Downloads" => {
                // This is handled in scan_downloads with age threshold
                Ok(false)
            },
            _ => Ok(false),
        }
    }
    
    fn get_file_description(&self, _path: &Path, category: &str) -> String {
        match category {
            "Downloads" => "Downloaded file".to_string(),
            "Trash" => "Trashed file".to_string(),
            "Safari Cache" => "Safari browser cache".to_string(),
            "Chrome Cache" => "Chrome browser cache".to_string(),
            "Firefox Cache" => "Firefox browser cache".to_string(),
            "Mail Attachments" => "Email attachment".to_string(),
            _ => format!("{} file", category),
        }
    }
    
    fn format_age(&self, age: std::time::Duration) -> String {
        let days = age.as_secs() / (24 * 3600);
        if days == 0 {
            "today".to_string()
        } else if days == 1 {
            "1 day old".to_string()
        } else {
            format!("{} days old", days)
        }
    }
}
