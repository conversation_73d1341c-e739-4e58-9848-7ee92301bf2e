use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::path::{Path, PathBuf};

/// Whitelist system for protected files and directories
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Whitelist {
    pub protected_paths: Vec<String>,
    pub protected_patterns: Vec<String>,
    pub protected_extensions: Vec<String>,
    pub system_directories: Vec<String>,
    pub user_data_directories: Vec<String>,
}

impl Default for Whitelist {
    fn default() -> Self {
        Self {
            protected_paths: vec![
                "~/Documents".to_string(),
                "~/Desktop".to_string(),
                "~/Pictures".to_string(),
                "~/Music".to_string(),
                "~/Movies".to_string(),
                "/System".to_string(),
                "/Library/CoreServices".to_string(),
                "/usr/bin".to_string(),
                "/usr/sbin".to_string(),
                "/bin".to_string(),
                "/sbin".to_string(),
                "/Applications".to_string(),
            ],
            protected_patterns: vec![
                "important".to_string(),
                "do-not-delete".to_string(),
                "keep".to_string(),
                "essential".to_string(),
            ],
            protected_extensions: vec![
                "key".to_string(),
                "pem".to_string(),
                "cert".to_string(),
                "keychain".to_string(),
            ],
            system_directories: vec![
                "/System".to_string(),
                "/Library".to_string(),
                "/usr".to_string(),
                "/bin".to_string(),
                "/sbin".to_string(),
                "/private".to_string(),
                "/etc".to_string(),
                "/var".to_string(),
            ],
            user_data_directories: vec![
                "~/Documents".to_string(),
                "~/Desktop".to_string(),
                "~/Pictures".to_string(),
                "~/Music".to_string(),
                "~/Movies".to_string(),
                "~/Public".to_string(),
                "~/Library/Application Support".to_string(),
                "~/Library/Preferences".to_string(),
                "~/Library/Accounts".to_string(),
                "~/Library/Calendars".to_string(),
                "~/Library/Contacts".to_string(),
                "~/Library/Mail".to_string(),
                "~/Library/Messages".to_string(),
                "~/Library/Safari".to_string(),
                "~/Library/Keychains".to_string(),
            ],
        }
    }
}

impl Whitelist {
    /// Check if a path is protected
    pub fn is_protected(&self, path: &Path) -> Result<bool> {
        // Expand all paths with tilde
        let expanded_protected_paths: Vec<PathBuf> = self.protected_paths
            .iter()
            .map(|p| crate::utils::FileUtils::expand_tilde(p))
            .collect();
        
        // Check if path is in protected paths
        for protected_path in &expanded_protected_paths {
            if path.starts_with(protected_path) {
                return Ok(true);
            }
        }
        
        // Check if path contains protected patterns
        if let Some(path_str) = path.to_str() {
            for pattern in &self.protected_patterns {
                if path_str.contains(pattern) {
                    return Ok(true);
                }
            }
        }
        
        // Check if file has protected extension
        if let Some(extension) = path.extension().and_then(|e| e.to_str()) {
            for protected_ext in &self.protected_extensions {
                if extension.to_lowercase() == protected_ext.to_lowercase() {
                    return Ok(true);
                }
            }
        }
        
        // Check if path is a system directory
        let expanded_system_dirs: Vec<PathBuf> = self.system_directories
            .iter()
            .map(|p| crate::utils::FileUtils::expand_tilde(p))
            .collect();
        
        for system_dir in &expanded_system_dirs {
            if path == system_dir {
                return Ok(true);
            }
        }
        
        // Check if path is a user data directory
        let expanded_user_dirs: Vec<PathBuf> = self.user_data_directories
            .iter()
            .map(|p| crate::utils::FileUtils::expand_tilde(p))
            .collect();
        
        for user_dir in &expanded_user_dirs {
            if path == user_dir {
                return Ok(true);
            }
        }
        
        Ok(false)
    }
    
    /// Check if a path is a system directory
    pub fn is_system_directory(&self, path: &Path) -> Result<bool> {
        let expanded_system_dirs: Vec<PathBuf> = self.system_directories
            .iter()
            .map(|p| crate::utils::FileUtils::expand_tilde(p))
            .collect();
        
        for system_dir in &expanded_system_dirs {
            if path.starts_with(system_dir) {
                return Ok(true);
            }
        }
        
        Ok(false)
    }
    
    /// Check if a path is a user data directory
    pub fn is_user_data_directory(&self, path: &Path) -> Result<bool> {
        let expanded_user_dirs: Vec<PathBuf> = self.user_data_directories
            .iter()
            .map(|p| crate::utils::FileUtils::expand_tilde(p))
            .collect();
        
        for user_dir in &expanded_user_dirs {
            if path.starts_with(user_dir) {
                return Ok(true);
            }
        }
        
        Ok(false)
    }
    
    /// Add a path to the whitelist
    pub fn add_protected_path(&mut self, path: &str) {
        if !self.protected_paths.contains(&path.to_string()) {
            self.protected_paths.push(path.to_string());
        }
    }
    
    /// Remove a path from the whitelist
    pub fn remove_protected_path(&mut self, path: &str) {
        self.protected_paths.retain(|p| p != path);
    }
    
    /// Add a pattern to the whitelist
    pub fn add_protected_pattern(&mut self, pattern: &str) {
        if !self.protected_patterns.contains(&pattern.to_string()) {
            self.protected_patterns.push(pattern.to_string());
        }
    }
    
    /// Add an extension to the whitelist
    pub fn add_protected_extension(&mut self, extension: &str) {
        let ext = extension.trim_start_matches('.');
        if !self.protected_extensions.contains(&ext.to_string()) {
            self.protected_extensions.push(ext.to_string());
        }
    }
    
    /// Check if a running application's data is protected
    pub fn is_running_app_protected(&self, path: &Path) -> Result<bool> {
        // Check if path is an application bundle
        if path.to_str().map_or(false, |s| s.ends_with(".app")) {
            // Get application name
            if let Some(app_name) = path.file_stem().and_then(|n| n.to_str()) {
                // Check if application is running
                let output = std::process::Command::new("pgrep")
                    .arg("-f")
                    .arg(app_name)
                    .output()?;
                
                if !output.stdout.is_empty() {
                    return Ok(true);
                }
            }
        }
        
        Ok(false)
    }
}
