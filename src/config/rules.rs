use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::path::Path;
use std::collections::HashMap;

/// Rules for cleaning different file types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CleanupRules {
    pub rules: HashMap<String, CleanupRule>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CleanupRule {
    pub name: String,
    pub description: String,
    pub paths: Vec<String>,
    pub patterns: Vec<String>,
    pub extensions: Vec<String>,
    pub min_age_days: u32,
    pub safe_to_delete: bool,
    pub requires_confirmation: bool,
}

impl Default for CleanupRules {
    fn default() -> Self {
        let mut rules = HashMap::new();
        
        // Application caches
        rules.insert("app_caches".to_string(), CleanupRule {
            name: "Application Caches".to_string(),
            description: "Temporary files created by applications".to_string(),
            paths: vec![
                "~/Library/Caches".to_string(),
                "/Library/Caches".to_string(),
            ],
            patterns: vec![
                "cache".to_string(),
                "tmp".to_string(),
            ],
            extensions: vec![
                "cache".to_string(),
                "tmp".to_string(),
            ],
            min_age_days: 7,
            safe_to_delete: true,
            requires_confirmation: false,
        });
        
        // System logs
        rules.insert("system_logs".to_string(), CleanupRule {
            name: "System Logs".to_string(),
            description: "System and application log files".to_string(),
            paths: vec![
                "~/Library/Logs".to_string(),
                "/var/log".to_string(),
            ],
            patterns: vec![
                "log".to_string(),
            ],
            extensions: vec![
                "log".to_string(),
            ],
            min_age_days: 30,
            safe_to_delete: true,
            requires_confirmation: false,
        });
        
        // Temporary files
        rules.insert("temp_files".to_string(), CleanupRule {
            name: "Temporary Files".to_string(),
            description: "Temporary files created by the system".to_string(),
            paths: vec![
                "/tmp".to_string(),
                "/private/var/folders".to_string(),
            ],
            patterns: vec![
                "temp".to_string(),
                "tmp".to_string(),
            ],
            extensions: vec![],
            min_age_days: 1,
            safe_to_delete: true,
            requires_confirmation: false,
        });
        
        // Downloads
        rules.insert("downloads".to_string(), CleanupRule {
            name: "Downloads".to_string(),
            description: "Old files in Downloads folder".to_string(),
            paths: vec![
                "~/Downloads".to_string(),
            ],
            patterns: vec![],
            extensions: vec![],
            min_age_days: 90,
            safe_to_delete: false,
            requires_confirmation: true,
        });
        
        // Trash
        rules.insert("trash".to_string(), CleanupRule {
            name: "Trash".to_string(),
            description: "Files in the Trash".to_string(),
            paths: vec![
                "~/.Trash".to_string(),
            ],
            patterns: vec![],
            extensions: vec![],
            min_age_days: 0,
            safe_to_delete: true,
            requires_confirmation: true,
        });
        
        // Xcode
        rules.insert("xcode".to_string(), CleanupRule {
            name: "Xcode".to_string(),
            description: "Xcode derived data and archives".to_string(),
            paths: vec![
                "~/Library/Developer/Xcode/DerivedData".to_string(),
                "~/Library/Developer/Xcode/Archives".to_string(),
                "~/Library/Developer/Xcode/iOS DeviceSupport".to_string(),
            ],
            patterns: vec![],
            extensions: vec![],
            min_age_days: 30,
            safe_to_delete: true,
            requires_confirmation: true,
        });
        
        // Node.js
        rules.insert("nodejs".to_string(), CleanupRule {
            name: "Node.js".to_string(),
            description: "Node.js modules and caches".to_string(),
            paths: vec![],
            patterns: vec![
                "node_modules".to_string(),
            ],
            extensions: vec![],
            min_age_days: 90,
            safe_to_delete: false,
            requires_confirmation: true,
        });
        
        // Python
        rules.insert("python".to_string(), CleanupRule {
            name: "Python".to_string(),
            description: "Python cache files".to_string(),
            paths: vec![],
            patterns: vec![
                "__pycache__".to_string(),
            ],
            extensions: vec![
                "pyc".to_string(),
            ],
            min_age_days: 0,
            safe_to_delete: true,
            requires_confirmation: false,
        });
        
        Self { rules }
    }
}

impl CleanupRules {
    /// Check if a file matches any rule
    pub fn matches_rule(&self, path: &Path, rule_id: &str) -> Result<bool> {
        if let Some(rule) = self.rules.get(rule_id) {
            return self.file_matches_rule(path, rule);
        }
        
        Ok(false)
    }
    
    /// Check if a file matches a specific rule
    pub fn file_matches_rule(&self, path: &Path, rule: &CleanupRule) -> Result<bool> {
        // Check if file is in one of the rule paths
        for rule_path in &rule.paths {
            let expanded_path = crate::utils::FileUtils::expand_tilde(rule_path);
            if path.starts_with(expanded_path) {
                return Ok(true);
            }
        }
        
        // Check if file matches any pattern
        if let Some(file_name) = path.file_name().and_then(|n| n.to_str()) {
            for pattern in &rule.patterns {
                if file_name.contains(pattern) {
                    return Ok(true);
                }
            }
        }
        
        // Check if file has any of the rule extensions
        if let Some(extension) = path.extension().and_then(|e| e.to_str()) {
            for rule_ext in &rule.extensions {
                if extension.to_lowercase() == rule_ext.to_lowercase() {
                    return Ok(true);
                }
            }
        }
        
        Ok(false)
    }
    
    /// Check if a file is safe to delete based on rules
    pub fn is_safe_to_delete(&self, path: &Path) -> Result<bool> {
        for (_, rule) in &self.rules {
            if self.file_matches_rule(path, rule)? {
                // Check file age if rule has minimum age
                if rule.min_age_days > 0 {
                    if let Ok(age_days) = crate::utils::FileUtils::get_file_age_days(path) {
                        if age_days < rule.min_age_days as u64 {
                            return Ok(false);
                        }
                    }
                }
                
                return Ok(rule.safe_to_delete);
            }
        }
        
        // If no rule matches, it's not safe to delete
        Ok(false)
    }
    
    /// Check if a file requires confirmation before deletion
    pub fn requires_confirmation(&self, path: &Path) -> Result<bool> {
        for (_, rule) in &self.rules {
            if self.file_matches_rule(path, rule)? {
                return Ok(rule.requires_confirmation);
            }
        }
        
        // If no rule matches, require confirmation for safety
        Ok(true)
    }
}
