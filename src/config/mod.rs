use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::path::{Path, PathBuf};

pub mod rules;
pub mod whitelist;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Config {
    pub general: GeneralConfig,
    pub categories: CategoriesConfig,
    pub safety: SafetyConfig,
    pub thresholds: ThresholdsConfig,
}

#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct GeneralConfig {
    pub backup_before_delete: bool,
    pub backup_retention_days: u32,
    pub confirm_deletions: bool,
    pub max_file_age_days: u32,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CategoriesConfig {
    pub cache: CategoryConfig,
    pub logs: LogsConfig,
    pub temp: CategoryConfig,
    pub downloads: DownloadsConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CategoryConfig {
    pub enabled: bool,
    pub auto_clean: bool,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct LogsConfig {
    pub enabled: bool,
    pub auto_clean: bool,
    pub keep_days: u32,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DownloadsConfig {
    pub enabled: bool,
    pub auto_clean: bool,
    pub age_threshold: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SafetyConfig {
    pub protected_paths: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThresholdsConfig {
    pub large_file_size_gb: f64,
    pub duplicate_min_size_mb: f64,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            general: GeneralConfig {
                backup_before_delete: true,
                backup_retention_days: 7, // Default to 7 days
                confirm_deletions: true,
                max_file_age_days: 30,
            },
            categories: CategoriesConfig {
                cache: CategoryConfig {
                    enabled: true,
                    auto_clean: true,
                },
                logs: LogsConfig {
                    enabled: true,
                    auto_clean: true,
                    keep_days: 7,
                },
                temp: CategoryConfig {
                    enabled: true,
                    auto_clean: true,
                },
                downloads: DownloadsConfig {
                    enabled: false,
                    auto_clean: false,
                    age_threshold: 90,
                },
            },
            safety: SafetyConfig {
                protected_paths: vec![
                    "~/Documents".to_string(),
                    "~/Desktop".to_string(),
                    "~/Pictures".to_string(),
                    "/System/Library/CoreServices".to_string(),
                    "/Applications".to_string(),
                    "/usr/bin".to_string(),
                    "/usr/sbin".to_string(),
                    "/bin".to_string(),
                    "/sbin".to_string(),
                ],
            },
            thresholds: ThresholdsConfig {
                large_file_size_gb: 1.0,
                duplicate_min_size_mb: 10.0,
            },
        }
    }
}

impl Config {
    pub fn new() -> Result<Self> {
        Ok(Self::default())
    }
    
    pub fn from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        let content = std::fs::read_to_string(path)?;
        let config: Config = toml::from_str(&content)?;
        Ok(config)
    }
    
    pub fn load_or_create_default() -> Result<Self> {
        let config_path = Self::get_config_path()?;

        if config_path.exists() {
            Self::from_file(&config_path)
        } else {
            let config = Self::default();
            config.save_to_file(&config_path)?;
            Ok(config)
        }
    }
    
    pub fn save_to_file<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        // Create config directory if it doesn't exist
        if let Some(parent) = path.as_ref().parent() {
            std::fs::create_dir_all(parent)?;
        }
        
        let toml_content = toml::to_string_pretty(self)?;
        std::fs::write(path, toml_content)?;
        Ok(())
    }
    
    pub fn get_config_path() -> Result<PathBuf> {
        let config_dir = Self::get_config_dir()?;
        Ok(config_dir.join("config.toml"))
    }
    
    pub fn get_config_dir() -> Result<PathBuf> {
        if let Some(path_str) = std::env::var_os("MACBOOK_CLEANER_CONFIG_DIR") {
            return Ok(PathBuf::from(path_str));
        }
        
        let home_dir = dirs::home_dir()
            .ok_or_else(|| anyhow::anyhow!("Could not find home directory"))?;
        
        Ok(home_dir.join(".macbook-cleaner"))
    }
    
    /// Expand tilde paths to absolute paths
    pub fn expand_protected_paths(&mut self) -> Result<()> {
        let home_dir = dirs::home_dir()
            .ok_or_else(|| anyhow::anyhow!("Could not find home directory"))?;
        
        for path in &mut self.safety.protected_paths {
            if path.starts_with("~/") {
                *path = home_dir.join(&path[2..]).to_string_lossy().to_string();
            }
        }
        
        Ok(())
    }
    
    /// Validate configuration values
    pub fn validate(&self) -> Result<()> {
        // Validate retention days
        if self.general.backup_retention_days == 0 {
            return Err(anyhow::anyhow!("backup_retention_days must be greater than 0"));
        }
        
        // Validate file age days
        if self.general.max_file_age_days == 0 {
            return Err(anyhow::anyhow!("max_file_age_days must be greater than 0"));
        }
        
        // Validate thresholds
        if self.thresholds.large_file_size_gb <= 0.0 {
            return Err(anyhow::anyhow!("large_file_size_gb must be greater than 0"));
        }
        
        if self.thresholds.duplicate_min_size_mb <= 0.0 {
            return Err(anyhow::anyhow!("duplicate_min_size_mb must be greater than 0"));
        }
        
        // Validate protected paths exist
        for path in &self.safety.protected_paths {
            let path_buf = PathBuf::from(path);
            if !path_buf.exists() {
                log::warn!("Protected path does not exist: {}", path);
            }
        }
        
        Ok(())
    }
}

/// Show current configuration
pub fn show_config(config: &Config) -> Result<()> {
    println!("\n⚙️  Current Configuration");
    println!("========================");
    
    println!("\n[General]");
    println!("  Backup before delete: {}", config.general.backup_before_delete);
    println!("  Backup retention days: {}", config.general.backup_retention_days);
    println!("  Confirm deletions: {}", config.general.confirm_deletions);
    println!("  Max file age days: {}", config.general.max_file_age_days);
    
    println!("\n[Categories]");
    println!("  Cache - enabled: {}, auto_clean: {}", 
             config.categories.cache.enabled, 
             config.categories.cache.auto_clean);
    println!("  Logs - enabled: {}, auto_clean: {}, keep_days: {}", 
             config.categories.logs.enabled, 
             config.categories.logs.auto_clean,
             config.categories.logs.keep_days);
    println!("  Temp - enabled: {}, auto_clean: {}", 
             config.categories.temp.enabled, 
             config.categories.temp.auto_clean);
    println!("  Downloads - enabled: {}, auto_clean: {}, age_threshold: {}", 
             config.categories.downloads.enabled, 
             config.categories.downloads.auto_clean,
             config.categories.downloads.age_threshold);
    
    println!("\n[Safety]");
    println!("  Protected paths:");
    for path in &config.safety.protected_paths {
        println!("    {}", path);
    }
    
    println!("\n[Thresholds]");
    println!("  Large file size: {} GB", config.thresholds.large_file_size_gb);
    println!("  Duplicate min size: {} MB", config.thresholds.duplicate_min_size_mb);
    
    Ok(())
}

/// Set configuration value
pub fn set_config_value(key_value: &str, _current_config: &Config) -> Result<()> {
    let parts: Vec<&str> = key_value.splitn(2, '=').collect();
    if parts.len() != 2 {
        return Err(anyhow::anyhow!("Invalid format. Use key=value"));
    }

    let key = parts[0].trim();
    let value = parts[1].trim();

    let config_path = Config::get_config_path()?;
    let mut config = Config::load_or_create_default()?; // Load current config

    match key {
        "general.backup_before_delete" => {
            config.general.backup_before_delete = value.parse::<bool>()
                .map_err(|_| anyhow::anyhow!("Invalid value for {}: Expected true or false", key))?;
        }
        "general.backup_retention_days" => {
            config.general.backup_retention_days = value.parse::<u32>()
                .map_err(|_| anyhow::anyhow!("Invalid value for {}: Expected a number", key))?;
        }
        "general.confirm_deletions" => {
            config.general.confirm_deletions = value.parse::<bool>()
                .map_err(|_| anyhow::anyhow!("Invalid value for {}: Expected true or false", key))?;
        }
        "general.max_file_age_days" => {
            config.general.max_file_age_days = value.parse::<u32>()
                .map_err(|_| anyhow::anyhow!("Invalid value for {}: Expected a number", key))?;
        }
        "thresholds.large_file_size_gb" => {
            config.thresholds.large_file_size_gb = value.parse::<f64>()
                .map_err(|_| anyhow::anyhow!("Invalid value for {}: Expected a floating point number", key))?;
        }
        "thresholds.duplicate_min_size_mb" => {
            config.thresholds.duplicate_min_size_mb = value.parse::<f64>()
                .map_err(|_| anyhow::anyhow!("Invalid value for {}: Expected a floating point number", key))?;
        }
        // Add more cases for other configuration options as needed
        _ => {
            return Err(anyhow::anyhow!("Unknown or unsupported configuration key: {}", key));
        }
    }

    config.save_to_file(&config_path)?;

    println!("Successfully updated configuration: {} = {}", key, value);
    Ok(())
}
