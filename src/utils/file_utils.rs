use anyhow::Result;
use std::path::{Path, PathBuf};
use std::fs;

/// Utility functions for file operations
pub struct FileUtils;

impl FileUtils {
    /// Check if a path is safe to delete
    pub fn is_safe_path(path: &Path, protected_paths: &[String]) -> bool {
        for protected in protected_paths {
            let protected_path = Path::new(protected);
            if path.starts_with(protected_path) {
                return false;
            }
        }
        true
    }
    
    /// Get human-readable file size
    pub fn format_size(size: u64) -> String {
        bytesize::ByteSize(size).to_string()
    }
    
    /// Get file extension in lowercase
    pub fn get_extension(path: &Path) -> Option<String> {
        path.extension()
            .and_then(|ext| ext.to_str())
            .map(|s| s.to_lowercase())
    }
    
    /// Check if file is hidden (starts with .)
    pub fn is_hidden(path: &Path) -> bool {
        path.file_name()
            .and_then(|name| name.to_str())
            .map(|name| name.starts_with('.'))
            .unwrap_or(false)
    }
    
    /// Get file age in days
    pub fn get_file_age_days(path: &Path) -> Result<u64> {
        let metadata = fs::metadata(path)?;
        let modified = metadata.modified()?;
        let now = std::time::SystemTime::now();
        let duration = now.duration_since(modified)?;
        Ok(duration.as_secs() / (24 * 3600))
    }
    
    /// Check if file is older than specified days
    pub fn is_older_than_days(path: &Path, days: u64) -> Result<bool> {
        let age = Self::get_file_age_days(path)?;
        Ok(age > days)
    }
    
    /// Expand tilde in path
    pub fn expand_tilde(path: &str) -> PathBuf {
        if path.starts_with("~/") {
            if let Some(home) = dirs::home_dir() {
                return home.join(&path[2..]);
            }
        }
        PathBuf::from(path)
    }
    
    /// Create directory if it doesn't exist
    pub fn ensure_dir_exists(path: &Path) -> Result<()> {
        if !path.exists() {
            fs::create_dir_all(path)?;
        }
        Ok(())
    }
    
    /// Get relative path from base
    pub fn get_relative_path(path: &Path, base: &Path) -> Result<PathBuf> {
        Ok(path.strip_prefix(base)?.to_path_buf())
    }
    
    /// Check if directory is empty
    pub fn is_empty_dir(path: &Path) -> Result<bool> {
        if !path.is_dir() {
            return Ok(false);
        }
        
        let mut entries = fs::read_dir(path)?;
        Ok(entries.next().is_none())
    }
    
    /// Get directory size recursively
    pub fn get_dir_size(path: &Path) -> Result<u64> {
        let mut size = 0;
        
        if path.is_file() {
            return Ok(fs::metadata(path)?.len());
        }
        
        for entry in walkdir::WalkDir::new(path)
            .follow_links(false)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_file() {
                if let Ok(metadata) = entry.metadata() {
                    size += metadata.len();
                }
            }
        }
        
        Ok(size)
    }
    
    /// Count files in directory recursively
    pub fn count_files(path: &Path) -> Result<usize> {
        let mut count = 0;
        
        for entry in walkdir::WalkDir::new(path)
            .follow_links(false)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_file() {
                count += 1;
            }
        }
        
        Ok(count)
    }
    
    /// Find files matching pattern
    pub fn find_files_by_pattern(path: &Path, pattern: &str) -> Result<Vec<PathBuf>> {
        let mut matches = Vec::new();
        
        for entry in walkdir::WalkDir::new(path)
            .follow_links(false)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_file() {
                if let Some(file_name) = entry.file_name().to_str() {
                    if file_name.contains(pattern) {
                        matches.push(entry.path().to_path_buf());
                    }
                }
            }
        }
        
        Ok(matches)
    }
    
    /// Find files by extension
    pub fn find_files_by_extension(path: &Path, extension: &str) -> Result<Vec<PathBuf>> {
        let mut matches = Vec::new();
        let ext_lower = extension.to_lowercase();
        
        for entry in walkdir::WalkDir::new(path)
            .follow_links(false)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_file() {
                if let Some(file_ext) = Self::get_extension(entry.path()) {
                    if file_ext == ext_lower {
                        matches.push(entry.path().to_path_buf());
                    }
                }
            }
        }
        
        Ok(matches)
    }
    
    /// Get file MIME type (simplified)
    pub fn get_mime_type(path: &Path) -> String {
        if let Some(extension) = Self::get_extension(path) {
            match extension.as_str() {
                "txt" | "md" | "rst" => "text/plain",
                "html" | "htm" => "text/html",
                "css" => "text/css",
                "js" => "application/javascript",
                "json" => "application/json",
                "xml" => "application/xml",
                "pdf" => "application/pdf",
                "zip" => "application/zip",
                "tar" => "application/x-tar",
                "gz" => "application/gzip",
                "jpg" | "jpeg" => "image/jpeg",
                "png" => "image/png",
                "gif" => "image/gif",
                "svg" => "image/svg+xml",
                "mp3" => "audio/mpeg",
                "wav" => "audio/wav",
                "mp4" => "video/mp4",
                "avi" => "video/x-msvideo",
                _ => "application/octet-stream",
            }.to_string()
        } else {
            "application/octet-stream".to_string()
        }
    }
    
    /// Check if file is binary
    pub fn is_binary_file(path: &Path) -> Result<bool> {
        let mut file = fs::File::open(path)?;
        let mut buffer = [0; 512];
        
        use std::io::Read;
        let bytes_read = file.read(&mut buffer)?;
        
        // Simple heuristic: if file contains null bytes, it's likely binary
        Ok(buffer[..bytes_read].contains(&0))
    }
    
    /// Get file permissions as string (Unix-style)
    pub fn get_permissions_string(path: &Path) -> Result<String> {
        use std::os::unix::fs::PermissionsExt;
        
        let metadata = fs::metadata(path)?;
        let permissions = metadata.permissions();
        let mode = permissions.mode();
        
        let mut perm_str = String::new();
        
        // File type
        if metadata.is_dir() {
            perm_str.push('d');
        } else if metadata.file_type().is_symlink() {
            perm_str.push('l');
        } else {
            perm_str.push('-');
        }
        
        // Owner permissions
        perm_str.push(if mode & 0o400 != 0 { 'r' } else { '-' });
        perm_str.push(if mode & 0o200 != 0 { 'w' } else { '-' });
        perm_str.push(if mode & 0o100 != 0 { 'x' } else { '-' });
        
        // Group permissions
        perm_str.push(if mode & 0o040 != 0 { 'r' } else { '-' });
        perm_str.push(if mode & 0o020 != 0 { 'w' } else { '-' });
        perm_str.push(if mode & 0o010 != 0 { 'x' } else { '-' });
        
        // Other permissions
        perm_str.push(if mode & 0o004 != 0 { 'r' } else { '-' });
        perm_str.push(if mode & 0o002 != 0 { 'w' } else { '-' });
        perm_str.push(if mode & 0o001 != 0 { 'x' } else { '-' });
        
        Ok(perm_str)
    }
}
