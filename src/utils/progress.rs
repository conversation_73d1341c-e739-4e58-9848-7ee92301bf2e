use anyhow::Result;
use indicatif::{ProgressBar, ProgressStyle, MultiProgress};
use console::style;
use std::time::Duration;

/// Progress bar utilities for CLI
pub struct ProgressManager {
    multi_progress: MultiProgress,
}

impl ProgressManager {
    pub fn new() -> Self {
        Self {
            multi_progress: MultiProgress::new(),
        }
    }
    
    /// Create a new progress bar for scanning
    pub fn create_scan_progress(&self, total_items: u64) -> ProgressBar {
        let progress = self.multi_progress.add(ProgressBar::new(total_items));
        progress.set_style(
            ProgressStyle::default_bar()
                .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {pos}/{len} ({eta}) {msg}")
                .unwrap()
                .progress_chars("#>-")
        );
        progress.enable_steady_tick(Duration::from_millis(100));
        progress
    }
    
    /// Create a new progress bar for cleaning
    pub fn create_clean_progress(&self, total_items: u64) -> ProgressBar {
        let progress = self.multi_progress.add(ProgressBar::new(total_items));
        progress.set_style(
            ProgressStyle::default_bar()
                .template("{spinner:.red} [{elapsed_precise}] [{bar:40.red/yellow}] {pos}/{len} ({eta}) {msg}")
                .unwrap()
                .progress_chars("█▓░")
        );
        progress.enable_steady_tick(Duration::from_millis(100));
        progress
    }
    
    /// Create a new progress bar for analysis
    pub fn create_analysis_progress(&self, total_items: u64) -> ProgressBar {
        let progress = self.multi_progress.add(ProgressBar::new(total_items));
        progress.set_style(
            ProgressStyle::default_bar()
                .template("{spinner:.blue} [{elapsed_precise}] [{bar:40.blue/white}] {pos}/{len} ({eta}) {msg}")
                .unwrap()
                .progress_chars("▰▱▱")
        );
        progress.enable_steady_tick(Duration::from_millis(100));
        progress
    }
    
    /// Create a spinner for indeterminate progress
    pub fn create_spinner(&self, message: &str) -> ProgressBar {
        let spinner = self.multi_progress.add(ProgressBar::new_spinner());
        spinner.set_style(
            ProgressStyle::default_spinner()
                .template("{spinner:.green} {msg}")
                .unwrap()
        );
        spinner.set_message(message.to_string());
        spinner.enable_steady_tick(Duration::from_millis(100));
        spinner
    }
    
    /// Create a progress bar for backup/restore operations
    pub fn create_backup_progress(&self, total_bytes: u64) -> ProgressBar {
        let progress = self.multi_progress.add(ProgressBar::new(total_bytes));
        progress.set_style(
            ProgressStyle::default_bar()
                .template("{spinner:.green} [{elapsed_precise}] [{bar:40.green/white}] {bytes}/{total_bytes} ({eta}) {msg}")
                .unwrap()
                .progress_chars("▰▰▱")
        );
        progress.enable_steady_tick(Duration::from_millis(100));
        progress
    }
}

/// Print a success message
pub fn print_success(message: &str) {
    println!("{} {}", style("✓").green().bold(), message);
}

/// Print an error message
pub fn print_error(message: &str) {
    eprintln!("{} {}", style("✗").red().bold(), message);
}

/// Print a warning message
pub fn print_warning(message: &str) {
    println!("{} {}", style("!").yellow().bold(), message);
}

/// Print an info message
pub fn print_info(message: &str) {
    println!("{} {}", style("i").blue().bold(), message);
}

/// Print a section header
pub fn print_section(title: &str) {
    println!("\n{}", style(title).bold().underlined());
}

/// Print a table row
pub fn print_table_row(columns: &[&str], widths: &[usize]) {
    let mut row = String::new();
    
    for (i, col) in columns.iter().enumerate() {
        let width = if i < widths.len() { widths[i] } else { 20 };
        let formatted = format!("{:<width$}", col, width = width);
        row.push_str(&formatted);
        
        if i < columns.len() - 1 {
            row.push_str("  ");
        }
    }
    
    println!("{}", row);
}

/// Print a table header
pub fn print_table_header(columns: &[&str], widths: &[usize]) {
    let styled_columns: Vec<String> = columns
        .iter()
        .map(|c| style(*c).bold().to_string())
        .collect();
    
    let styled_refs: Vec<&str> = styled_columns
        .iter()
        .map(|s| s.as_str())
        .collect();
    
    print_table_row(&styled_refs, widths);
    
    // Print separator
    let mut separator = String::new();
    for (i, width) in widths.iter().enumerate() {
        separator.push_str(&"-".repeat(*width));
        
        if i < widths.len() - 1 {
            separator.push_str("  ");
        }
    }
    
    println!("{}", separator);
}

/// Format bytes as human-readable size
pub fn format_bytes(bytes: u64) -> String {
    bytesize::ByteSize(bytes).to_string()
}

/// Format duration in seconds as human-readable time
pub fn format_duration(seconds: u64) -> String {
    if seconds < 60 {
        format!("{}s", seconds)
    } else if seconds < 3600 {
        format!("{}m {}s", seconds / 60, seconds % 60)
    } else {
        format!("{}h {}m {}s", seconds / 3600, (seconds % 3600) / 60, seconds % 60)
    }
}

/// Ask user for confirmation
pub fn ask_confirmation(prompt: &str) -> Result<bool> {
    use std::io::{self, Write};
    
    print!("{} [y/N]: ", prompt);
    io::stdout().flush()?;
    
    let mut input = String::new();
    io::stdin().read_line(&mut input)?;
    
    Ok(input.trim().to_lowercase() == "y" || input.trim().to_lowercase() == "yes")
}
