use clap::{Parser, Subcommand};
use anyhow::Result;
use log::{info, error};

mod scanner;
mod cleaner;
mod analyzer;
mod config;
mod utils;

use config::Config;

#[derive(Parser)]
#[command(name = "macbook-cleaner")]
#[command(about = "A comprehensive command-line tool for macOS that safely identifies, analyzes, and removes unnecessary files to free up disk space")]
#[command(version = "0.1.0")]
struct Cli {
    #[command(subcommand)]
    command: Commands,
    
    /// Enable verbose logging
    #[arg(short, long)]
    verbose: bool,
    
    /// Configuration file path
    #[arg(short, long)]
    config: Option<String>,
}

#[derive(Subcommand)]
enum Commands {
    /// Scan and analyze disk usage
    Scan {
        /// Perform deep scan with detailed analysis
        #[arg(long)]
        deep: bool,
        
        /// Scan specific category only
        #[arg(long)]
        category: Option<String>,
    },
    
    /// Clean files with interactive mode
    Clean {
        /// Preview changes without actually deleting files
        #[arg(long)]
        dry_run: bool,
        
        /// Auto-clean safe categories without prompts
        #[arg(long)]
        auto: bool,
        
        /// Clean specific category only
        #[arg(long)]
        category: Option<String>,
    },
    
    /// Analyze specific directories
    Analyze {
        /// Path to analyze
        path: String,
        
        /// Find duplicate files
        #[arg(long)]
        duplicates: bool,
        
        /// Find large files
        #[arg(long)]
        large_files: bool,
    },
    
    /// Configuration management
    Config {
        /// Set configuration value (key=value)
        #[arg(long)]
        set: Option<String>,
        
        /// Show current configuration
        #[arg(long)]
        show: bool,
    },
    
    /// Restore from backup
    Restore {
        /// List available backups
        #[arg(long)]
        list: bool,
        
        /// Restore specific backup file
        #[arg(long)]
        file: Option<String>,
    },
}

#[tokio::main]
async fn main() -> Result<()> {
    let cli = Cli::parse();
    
    // Initialize logging
    if cli.verbose {
        env_logger::Builder::from_default_env()
            .filter_level(log::LevelFilter::Debug)
            .init();
    } else {
        env_logger::Builder::from_default_env()
            .filter_level(log::LevelFilter::Info)
            .init();
    }
    
    info!("Starting MacBook Cleaner v0.1.0");
    
    // Load configuration
    let config = match cli.config {
        Some(config_path) => Config::from_file(&config_path)?,
        None => Config::new()?,
    };
    
    // Execute command
    match cli.command {
        Commands::Scan { deep, category } => {
            info!("Starting scan operation");
            scanner::run_scan(deep, category, &config).await?;
        },
        Commands::Clean { dry_run, auto, category } => {
            info!("Starting clean operation");
            cleaner::run_clean(dry_run, auto, category, &config).await?;
        },
        Commands::Analyze { path, duplicates, large_files } => {
            info!("Starting analysis of: {}", path);
            analyzer::run_analysis(&path, duplicates, large_files, &config).await?;
        },
        Commands::Config { set, show } => {
            if show {
                config::show_config(&config)?;
            }
            if let Some(key_value) = set {
                config::set_config_value(&key_value, &config)?;
            }
        },
        Commands::Restore { list, file } => {
            if list {
                cleaner::list_backups(&config)?;
            }
            if let Some(backup_file) = file {
                cleaner::restore_backup(&backup_file, &config)?;
            }
        },
    }
    
    info!("Operation completed successfully");
    Ok(())
}
