# Rust build artifacts
/target/
**/*.rs.bk
Cargo.lock

# macOS system files
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# IDE files
.idea/
.vscode/
*.sublime-project
*.sublime-workspace
.project
.classpath
.settings/
*.swp
*.swo

# Test files
*.test
*.tmp

# Backup files
*.bak
*.backup
*~

# Log files
*.log
logs/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build output
/dist/
/build/

# Documentation
/doc/
/docs/build/

# Dependency directories
node_modules/
jspm_packages/
bower_components/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
