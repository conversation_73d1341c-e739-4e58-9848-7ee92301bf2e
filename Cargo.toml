[package]
name = "macbook-cleaner"
version = "0.1.0"
edition = "2021"
authors = ["<PERSON> <<EMAIL>>"]
description = "A comprehensive command-line tool for macOS that safely identifies, analyzes, and removes unnecessary files to free up disk space"
license = "MIT"
repository = "https://github.com/user/macbook-cleaner"
keywords = ["macos", "cleaner", "storage", "cli", "disk-space"]
categories = ["command-line-utilities", "filesystem"]

[dependencies]
# CLI argument parsing
clap = { version = "4.0", features = ["derive", "color"] }

# Async runtime
tokio = { version = "1.0", features = ["full"] }

# Serialization for configuration
serde = { version = "1.0", features = ["derive"] }
toml = "0.8"

# Progress bars and user interface
indicatif = "0.17"
console = "0.15"

# File system operations
walkdir = "2.3"
glob = "0.3"

# Date and time handling
chrono = { version = "0.4", features = ["serde"] }

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Logging
log = "0.4"
env_logger = "0.10"

# System information
sysinfo = "0.29"

# Compression for backups
flate2 = "1.0"
tar = "0.4"

# JSON handling for metadata
serde_json = "1.0"

# Cross-platform path handling
path-absolutize = "3.0"

# File hashing for duplicate detection
sha2 = "0.10"

# Human-readable byte sizes
bytesize = "1.1"

# Directory utilities
dirs = "5.0"

# Random number generation for secure delete
rand = "0.8"

[dev-dependencies]
tempfile = "3.0"
assert_cmd = "2.0"
predicates = "3.0"

[lib]
name = "macbook_cleaner"
path = "src/lib.rs"

[[bin]]
name = "macbook-cleaner"
path = "src/main.rs"

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true
